<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Link Generator - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="container mx-auto px-4 max-w-md">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Payment Link Generator
                </h1>
                <p class="text-gray-600">
                    Tạo link thanh toán nhanh chóng và an toàn
                </p>
            </div>

            <div id="form-container" class="bg-white rounded-lg shadow-md p-6">
                <form id="payment-form" class="space-y-4">
                    <div id="error-message" class="hidden p-3 bg-red-100 border border-red-400 text-red-700 rounded"></div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Số tiền
                        </label>
                        <input
                            type="number"
                            step="0.01"
                            id="amount"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Nhập số tiền"
                        />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Loại tiền
                        </label>
                        <select
                            id="currency"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="VND">VND (Việt Nam Đồng)</option>
                            <option value="USDT">USDT (Tether)</option>
                        </select>
                    </div>

                    <div id="vnd-fields">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Số tài khoản ngân hàng
                            </label>
                            <input
                                type="text"
                                id="bankAccount"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Nhập số tài khoản"
                            />
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Mã ngân hàng
                            </label>
                            <select
                                id="bankCode"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Chọn ngân hàng</option>
                                <option value="VCB">Vietcombank (VCB)</option>
                                <option value="TCB">Techcombank (TCB)</option>
                                <option value="VTB">Vietinbank (VTB)</option>
                                <option value="BIDV">BIDV</option>
                                <option value="ACB">ACB</option>
                                <option value="MB">MB Bank</option>
                            </select>
                        </div>
                    </div>

                    <div id="usdt-fields" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Địa chỉ ví USDT (TRC20)
                        </label>
                        <input
                            type="text"
                            id="walletAddress"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Nhập địa chỉ ví USDT"
                        />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Thời hạn
                        </label>
                        <select
                            id="expiresIn"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="3h">3 giờ</option>
                            <option value="1d">1 ngày</option>
                            <option value="3d">3 ngày</option>
                            <option value="7d">7 ngày</option>
                        </select>
                    </div>

                    <button
                        type="submit"
                        id="submit-btn"
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        Tạo Link Thanh Toán
                    </button>
                </form>
            </div>

            <div id="result-container" class="hidden bg-white rounded-lg shadow-md p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Link Đã Tạo!</h2>
                    <p class="text-green-600">✅ Thành công</p>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Số tiền:</label>
                        <p id="result-amount" class="text-lg font-semibold"></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Link thanh toán:</label>
                        <div class="flex items-center space-x-2">
                            <input
                                type="text"
                                id="payment-url"
                                readonly
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                            />
                            <button
                                onclick="copyToClipboard()"
                                class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                Copy
                            </button>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <button
                            onclick="openPaymentLink()"
                            class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
                        >
                            Mở Link Thanh Toán
                        </button>
                        
                        <button
                            onclick="createNew()"
                            class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
                        >
                            Tạo Link Mới
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentResult = null;

        // Toggle currency fields
        document.getElementById('currency').addEventListener('change', function() {
            const currency = this.value;
            const vndFields = document.getElementById('vnd-fields');
            const usdtFields = document.getElementById('usdt-fields');
            
            if (currency === 'VND') {
                vndFields.style.display = 'block';
                usdtFields.style.display = 'none';
                document.getElementById('bankAccount').required = true;
                document.getElementById('bankCode').required = true;
                document.getElementById('walletAddress').required = false;
            } else {
                vndFields.style.display = 'none';
                usdtFields.style.display = 'block';
                document.getElementById('bankAccount').required = false;
                document.getElementById('bankCode').required = false;
                document.getElementById('walletAddress').required = true;
            }
        });

        // Form submission
        document.getElementById('payment-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const errorDiv = document.getElementById('error-message');
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Đang tạo...';
            errorDiv.classList.add('hidden');

            try {
                const formData = {
                    amount: parseFloat(document.getElementById('amount').value),
                    currency: document.getElementById('currency').value,
                    expiresIn: document.getElementById('expiresIn').value
                };

                if (formData.currency === 'VND') {
                    formData.bankAccount = document.getElementById('bankAccount').value;
                    formData.bankCode = document.getElementById('bankCode').value;
                } else {
                    formData.walletAddress = document.getElementById('walletAddress').value;
                }

                const response = await fetch('http://localhost:3000/api/create-payment-link', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();
                
                if (result.error) {
                    errorDiv.textContent = result.message;
                    errorDiv.classList.remove('hidden');
                } else {
                    currentResult = result.data;
                    showResult();
                }
            } catch (err) {
                errorDiv.textContent = 'Có lỗi xảy ra khi tạo link thanh toán';
                errorDiv.classList.remove('hidden');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Tạo Link Thanh Toán';
            }
        });

        function showResult() {
            const formContainer = document.getElementById('form-container');
            const resultContainer = document.getElementById('result-container');
            
            formContainer.classList.add('hidden');
            resultContainer.classList.remove('hidden');
            
            const paymentUrl = `${window.location.origin}/pay.html?hash=${currentResult.hash}`;
            
            document.getElementById('result-amount').textContent = 
                `${currentResult.amount.toLocaleString()} ${currentResult.currency}`;
            document.getElementById('payment-url').value = paymentUrl;
        }

        function copyToClipboard() {
            const paymentUrl = document.getElementById('payment-url');
            paymentUrl.select();
            document.execCommand('copy');
            alert('Đã sao chép!');
        }

        function openPaymentLink() {
            const paymentUrl = document.getElementById('payment-url').value;
            window.open(paymentUrl, '_blank');
        }

        function createNew() {
            const formContainer = document.getElementById('form-container');
            const resultContainer = document.getElementById('result-container');
            
            formContainer.classList.remove('hidden');
            resultContainer.classList.add('hidden');
            
            document.getElementById('payment-form').reset();
            currentResult = null;
        }
    </script>
</body>
</html>
