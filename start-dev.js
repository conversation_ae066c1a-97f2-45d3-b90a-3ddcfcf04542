#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Payment Link Development Environment...\n');

// Start backend
console.log('📡 Starting Backend (Port 3000)...');
const backend = spawn('npx', ['ts-node', 'src/test-server.ts'], {
  cwd: __dirname,
  stdio: ['inherit', 'pipe', 'pipe']
});

backend.stdout.on('data', (data) => {
  console.log(`[Backend] ${data.toString().trim()}`);
});

backend.stderr.on('data', (data) => {
  console.error(`[Backend Error] ${data.toString().trim()}`);
});

// Wait a bit for backend to start
setTimeout(() => {
  console.log('\n💻 Starting Frontend (Port 3001)...');
  
  const frontend = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, 'frontend'),
    stdio: ['inherit', 'pipe', 'pipe']
  });

  frontend.stdout.on('data', (data) => {
    console.log(`[Frontend] ${data.toString().trim()}`);
  });

  frontend.stderr.on('data', (data) => {
    console.error(`[Frontend Error] ${data.toString().trim()}`);
  });

  frontend.on('close', (code) => {
    console.log(`\n❌ Frontend process exited with code ${code}`);
    process.exit(code);
  });

}, 3000);

backend.on('close', (code) => {
  console.log(`\n❌ Backend process exited with code ${code}`);
  process.exit(code);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development environment...');
  backend.kill('SIGINT');
  process.exit(0);
});

console.log('\n📋 Development URLs:');
console.log('   Backend:  http://localhost:3000');
console.log('   Frontend: http://localhost:3001');
console.log('   API Docs: http://localhost:3000/api/health');
console.log('\n💡 Press Ctrl+C to stop both servers\n');
