'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import QRCode from 'qrcode';
import { paymentAPI, PaymentLink, formatCurrency, formatDate, getTimeRemaining, isExpired } from '@/lib/api';
import { Clock, Copy, Check, AlertCircle, CheckCircle, Upload } from 'lucide-react';

export default function PaymentPage() {
  const params = useParams();
  const hash = params.hash as string;
  
  const [paymentLink, setPaymentLink] = useState<PaymentLink | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [uploadingReceipt, setUploadingReceipt] = useState(false);

  useEffect(() => {
    if (hash) {
      fetchPaymentLink();
    }
  }, [hash]);

  useEffect(() => {
    if (paymentLink) {
      // Generate QR code for payment info
      let qrData = '';
      if (paymentLink.currency === 'VND') {
        qrData = `Bank: ${paymentLink.bankCode}\nAccount: ${paymentLink.bankAccount}\nAmount: ${formatCurrency(paymentLink.amount, paymentLink.currency)}`;
      } else {
        qrData = paymentLink.walletAddress || '';
      }

      QRCode.toDataURL(qrData, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      })
        .then(setQrCodeUrl)
        .catch(console.error);

      // Update time remaining
      const updateTime = () => {
        setTimeRemaining(getTimeRemaining(paymentLink.expiresAt));
      };
      updateTime();
      const interval = setInterval(updateTime, 60000);
      return () => clearInterval(interval);
    }
  }, [paymentLink]);

  const fetchPaymentLink = async () => {
    try {
      setLoading(true);
      const data = await paymentAPI.getPaymentLink(hash);
      setPaymentLink(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể tải thông tin thanh toán');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !paymentLink) return;

    try {
      setUploadingReceipt(true);
      await paymentAPI.uploadReceipt(paymentLink.hash, file);
      // Refresh payment link data
      await fetchPaymentLink();
      alert('Upload biên lai thành công!');
    } catch (err) {
      alert('Lỗi upload biên lai: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setUploadingReceipt(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin thanh toán...</p>
        </div>
      </div>
    );
  }

  if (error || !paymentLink) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Lỗi</h2>
          <p className="text-gray-600 mb-4">{error || 'Không tìm thấy thông tin thanh toán'}</p>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Về Trang Chủ
          </button>
        </div>
      </div>
    );
  }

  const expired = isExpired(paymentLink.expiresAt);

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Thông Tin Thanh Toán
          </h1>
          {paymentLink.isPaid ? (
            <div className="flex items-center justify-center text-green-600">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span>Đã thanh toán</span>
            </div>
          ) : expired ? (
            <div className="flex items-center justify-center text-red-600">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>Đã hết hạn</span>
            </div>
          ) : (
            <div className="flex items-center justify-center text-orange-600">
              <Clock className="w-5 h-5 mr-2" />
              <span>Còn lại: {timeRemaining}</span>
            </div>
          )}
        </div>

        {/* Payment Info Card */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {formatCurrency(paymentLink.amount, paymentLink.currency)}
            </h2>
            <p className="text-gray-600">Số tiền cần thanh toán</p>
          </div>

          {/* Payment Method */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Thông tin {paymentLink.currency === 'VND' ? 'chuyển khoản' : 'ví điện tử'}
            </h3>
            
            {paymentLink.currency === 'VND' ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tài khoản:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{paymentLink.bankAccount}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.bankAccount!)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Ngân hàng:</span>
                  <span className="font-semibold">{paymentLink.bankCode}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tiền:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{formatCurrency(paymentLink.amount, paymentLink.currency)}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.amount.toString())}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600 block mb-2">Địa chỉ ví USDT (TRC20):</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-sm break-all flex-1">{paymentLink.walletAddress}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.walletAddress!)}
                      className="text-blue-600 hover:text-blue-700 flex-shrink-0"
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tiền:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{paymentLink.amount} USDT</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.amount.toString())}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* QR Code */}
          {qrCodeUrl && (
            <div className="border-t border-gray-200 pt-6 text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Mã QR</h3>
              <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                <img src={qrCodeUrl} alt="QR Code" className="w-48 h-48" />
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Quét mã QR để sao chép thông tin thanh toán
              </p>
            </div>
          )}
        </div>

        {/* Receipt Upload */}
        {!paymentLink.isPaid && !expired && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Biên Lai</h3>
            <p className="text-gray-600 mb-4">
              Sau khi chuyển khoản, vui lòng upload ảnh biên lai để xác nhận thanh toán.
            </p>
            <div className="flex items-center space-x-4">
              <label className="flex-1">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  disabled={uploadingReceipt}
                  className="hidden"
                />
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">
                    {uploadingReceipt ? 'Đang upload...' : 'Chọn ảnh biên lai'}
                  </p>
                </div>
              </label>
            </div>
            {paymentLink.receiptUrl && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">
                  ✅ Đã upload biên lai lúc {formatDate(paymentLink.uploadedAt!)}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Payment Status */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Trạng Thái</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Tạo lúc:</span>
              <span className="font-semibold">{formatDate(paymentLink.createdAt)}</span>
            </div>
            {paymentLink.expiresAt && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Hết hạn:</span>
                <span className="font-semibold">{formatDate(paymentLink.expiresAt)}</span>
              </div>
            )}
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Trạng thái:</span>
              <span className={`font-semibold ${
                paymentLink.isPaid ? 'text-green-600' : 
                expired ? 'text-red-600' : 'text-orange-600'
              }`}>
                {paymentLink.isPaid ? 'Đã thanh toán' : 
                 expired ? 'Đã hết hạn' : 'Chờ thanh toán'}
              </span>
            </div>
            {paymentLink.paidAt && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Thanh toán lúc:</span>
                <span className="font-semibold text-green-600">{formatDate(paymentLink.paidAt)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
