'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';

export default function PaymentPage() {
  const params = useParams();
  const hash = params.hash as string;
  
  const [paymentLink, setPaymentLink] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (hash) {
      fetchPaymentLink();
    }
  }, [hash]);

  const fetchPaymentLink = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:3000/api/pay/${hash}`);
      const result = await response.json();
      
      if (result.error) {
        setError(result.message);
      } else {
        setPaymentLink(result.data);
      }
    } catch (err) {
      setError('Không thể tải thông tin thanh toán');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const formatCurrency = (amount: number, currency: string) => {
    if (currency === 'VND') {
      return amount.toLocaleString() + ' VND';
    }
    return amount + ' ' + currency;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false;
    return new Date() > new Date(expiresAt);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin thanh toán...</p>
        </div>
      </div>
    );
  }

  if (error || !paymentLink) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">❌</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Lỗi</h2>
          <p className="text-gray-600 mb-4">{error || 'Không tìm thấy thông tin thanh toán'}</p>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Về Trang Chủ
          </button>
        </div>
      </div>
    );
  }

  const expired = isExpired(paymentLink.expiresAt);

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Thông Tin Thanh Toán
          </h1>
          {paymentLink.isPaid ? (
            <div className="text-green-600">
              <span>✅ Đã thanh toán</span>
            </div>
          ) : expired ? (
            <div className="text-red-600">
              <span>❌ Đã hết hạn</span>
            </div>
          ) : (
            <div className="text-orange-600">
              <span>⏳ Chờ thanh toán</span>
            </div>
          )}
        </div>

        {/* Payment Info Card */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {formatCurrency(paymentLink.amount, paymentLink.currency)}
            </h2>
            <p className="text-gray-600">Số tiền cần thanh toán</p>
          </div>

          {/* Payment Method */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Thông tin {paymentLink.currency === 'VND' ? 'chuyển khoản' : 'ví điện tử'}
            </h3>
            
            {paymentLink.currency === 'VND' ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tài khoản:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{paymentLink.bankAccount}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.bankAccount)}
                      className="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                    >
                      {copied ? '✅' : '📋'}
                    </button>
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Ngân hàng:</span>
                  <span className="font-semibold">{paymentLink.bankCode}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tiền:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{formatCurrency(paymentLink.amount, paymentLink.currency)}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.amount.toString())}
                      className="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                    >
                      {copied ? '✅' : '📋'}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600 block mb-2">Địa chỉ ví USDT (TRC20):</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-sm break-all flex-1">{paymentLink.walletAddress}</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.walletAddress)}
                      className="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm flex-shrink-0"
                    >
                      {copied ? '✅' : '📋'}
                    </button>
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <span className="text-gray-600">Số tiền:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold">{paymentLink.amount} USDT</span>
                    <button
                      onClick={() => copyToClipboard(paymentLink.amount.toString())}
                      className="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                    >
                      {copied ? '✅' : '📋'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Payment Status */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Trạng Thái</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Tạo lúc:</span>
              <span className="font-semibold">{formatDate(paymentLink.createdAt)}</span>
            </div>
            {paymentLink.expiresAt && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Hết hạn:</span>
                <span className="font-semibold">{formatDate(paymentLink.expiresAt)}</span>
              </div>
            )}
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Trạng thái:</span>
              <span className={`font-semibold ${
                paymentLink.isPaid ? 'text-green-600' : 
                expired ? 'text-red-600' : 'text-orange-600'
              }`}>
                {paymentLink.isPaid ? 'Đã thanh toán' : 
                 expired ? 'Đã hết hạn' : 'Chờ thanh toán'}
              </span>
            </div>
            {paymentLink.paidAt && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Thanh toán lúc:</span>
                <span className="font-semibold text-green-600">{formatDate(paymentLink.paidAt)}</span>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 text-center">
          <button
            onClick={() => window.location.href = '/'}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
          >
            Tạo Link Mới
          </button>
        </div>
      </div>
    </div>
  );
}
