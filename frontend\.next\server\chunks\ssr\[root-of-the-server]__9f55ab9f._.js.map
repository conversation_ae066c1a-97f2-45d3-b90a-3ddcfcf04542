{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types\nexport interface CreatePaymentLinkRequest {\n  amount: number;\n  currency: 'VND' | 'USDT';\n  walletAddress?: string;\n  bankAccount?: string;\n  bankCode?: string;\n  expiresIn?: string;\n}\n\nexport interface PaymentLink {\n  id: number;\n  hash: string;\n  amount: number;\n  currency: 'VND' | 'USDT';\n  walletAddress?: string;\n  bankAccount?: string;\n  bankCode?: string;\n  isPaid: boolean;\n  paidAt?: string;\n  receiptUrl?: string;\n  uploadedAt?: string;\n  expiresAt?: string;\n  createdAt: string;\n  isExpired: boolean;\n}\n\nexport interface ApiResponse<T> {\n  error: boolean;\n  message: string;\n  data?: T;\n  timestamp: string;\n}\n\n// API functions\nexport const paymentAPI = {\n  // Create payment link\n  async createPaymentLink(data: CreatePaymentLinkRequest): Promise<PaymentLink> {\n    const response = await api.post<ApiResponse<PaymentLink>>('/create-payment-link', data);\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Get payment link by hash\n  async getPaymentLink(hash: string): Promise<PaymentLink> {\n    const response = await api.get<ApiResponse<PaymentLink>>(`/pay/${hash}`);\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Mark payment as paid\n  async markAsPaid(hash: string, confirmed: boolean): Promise<PaymentLink> {\n    const response = await api.post<ApiResponse<PaymentLink>>(`/payment/${hash}/mark-paid`, { confirmed });\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Upload receipt\n  async uploadReceipt(hash: string, file: File): Promise<{ receiptUrl: string; uploadedAt: string }> {\n    const formData = new FormData();\n    formData.append('receipt', file);\n    \n    const response = await api.post<ApiResponse<{ receiptUrl: string; uploadedAt: string }>>(\n      `/payment/${hash}/upload-proof`, \n      formData,\n      {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      }\n    );\n    \n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Health check\n  async healthCheck(): Promise<{ status: string; database: string; timestamp: string }> {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\n// Utility functions\nexport function formatCurrency(amount: number, currency: 'VND' | 'USDT'): string {\n  if (currency === 'VND') {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(amount);\n  } else {\n    return `${amount.toFixed(2)} USDT`;\n  }\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleString('vi-VN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function isExpired(expiresAt?: string): boolean {\n  if (!expiresAt) return false;\n  return new Date() > new Date(expiresAt);\n}\n\nexport function getTimeRemaining(expiresAt?: string): string {\n  if (!expiresAt) return '';\n  \n  const now = new Date().getTime();\n  const expiry = new Date(expiresAt).getTime();\n  const diff = expiry - now;\n  \n  if (diff <= 0) return 'Đã hết hạn';\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  \n  if (hours > 0) {\n    return `${hours}h ${minutes}m`;\n  } else {\n    return `${minutes}m`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA,MAAM,eAAe,iEAAmC;AAExD,MAAM,MAAM,MAAM,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAqCO,MAAM,aAAa;IACxB,sBAAsB;IACtB,MAAM,mBAAkB,IAA8B;QACpD,MAAM,WAAW,MAAM,IAAI,IAAI,CAA2B,wBAAwB;QAClF,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,2BAA2B;IAC3B,MAAM,gBAAe,IAAY;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAA2B,CAAC,KAAK,EAAE,MAAM;QACvE,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,uBAAuB;IACvB,MAAM,YAAW,IAAY,EAAE,SAAkB;QAC/C,MAAM,WAAW,MAAM,IAAI,IAAI,CAA2B,CAAC,SAAS,EAAE,KAAK,UAAU,CAAC,EAAE;YAAE;QAAU;QACpG,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,iBAAiB;IACjB,MAAM,eAAc,IAAY,EAAE,IAAU;QAC1C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,WAAW;QAE3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAC7B,CAAC,SAAS,EAAE,KAAK,aAAa,CAAC,EAC/B,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAGF,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,eAAe;IACf,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,SAAS,eAAe,MAAc,EAAE,QAAwB;IACrE,IAAI,aAAa,OAAO;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ,OAAO;QACL,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC;IACpC;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;QAClD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,UAAU,SAAkB;IAC1C,IAAI,CAAC,WAAW,OAAO;IACvB,OAAO,IAAI,SAAS,IAAI,KAAK;AAC/B;AAEO,SAAS,iBAAiB,SAAkB;IACjD,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,MAAM,IAAI,OAAO,OAAO;IAC9B,MAAM,SAAS,IAAI,KAAK,WAAW,OAAO;IAC1C,MAAM,OAAO,SAAS;IAEtB,IAAI,QAAQ,GAAG,OAAO;IAEtB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IAEjE,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;IAChC,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/components/CreatePaymentForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { paymentAPI, CreatePaymentLinkRequest, PaymentLink } from '@/lib/api';\n\n// Validation schema\nconst createPaymentSchema = z.object({\n  amount: z.number().min(0.01, 'Số tiền phải lớn hơn 0'),\n  currency: z.enum(['VND', 'USDT'], { required_error: 'Vui lòng chọn loại tiền' }),\n  walletAddress: z.string().optional(),\n  bankAccount: z.string().optional(),\n  bankCode: z.string().optional(),\n  expiresIn: z.string().default('1d'),\n}).refine((data) => {\n  if (data.currency === 'USDT' && !data.walletAddress) {\n    return false;\n  }\n  if (data.currency === 'VND' && (!data.bankAccount || !data.bankCode)) {\n    return false;\n  }\n  return true;\n}, {\n  message: '<PERSON>ui lòng điền đầy đủ thông tin thanh toán',\n  path: ['currency'],\n});\n\ntype CreatePaymentFormData = z.infer<typeof createPaymentSchema>;\n\ninterface CreatePaymentFormProps {\n  onSuccess: (paymentLink: PaymentLink) => void;\n}\n\nexport default function CreatePaymentForm({ onSuccess }: CreatePaymentFormProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors },\n    reset,\n  } = useForm<CreatePaymentFormData>({\n    resolver: zodResolver(createPaymentSchema),\n    defaultValues: {\n      currency: 'VND',\n      expiresIn: '1d',\n    },\n  });\n\n  const currency = watch('currency');\n\n  const onSubmit = async (data: CreatePaymentFormData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const paymentLink = await paymentAPI.createPaymentLink(data as CreatePaymentLinkRequest);\n      onSuccess(paymentLink);\n      reset();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Tạo Link Thanh Toán</h2>\n      \n      {error && (\n        <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n        {/* Amount */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Số tiền\n          </label>\n          <input\n            type=\"number\"\n            step=\"0.01\"\n            {...register('amount', { valueAsNumber: true })}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Nhập số tiền\"\n          />\n          {errors.amount && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.amount.message}</p>\n          )}\n        </div>\n\n        {/* Currency */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Loại tiền\n          </label>\n          <select\n            {...register('currency')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"VND\">VND (Việt Nam Đồng)</option>\n            <option value=\"USDT\">USDT (Tether)</option>\n          </select>\n          {errors.currency && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.currency.message}</p>\n          )}\n        </div>\n\n        {/* Payment Method Fields */}\n        {currency === 'VND' && (\n          <>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Số tài khoản ngân hàng\n              </label>\n              <input\n                type=\"text\"\n                {...register('bankAccount')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Nhập số tài khoản\"\n              />\n              {errors.bankAccount && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.bankAccount.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Mã ngân hàng\n              </label>\n              <select\n                {...register('bankCode')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">Chọn ngân hàng</option>\n                <option value=\"VCB\">Vietcombank (VCB)</option>\n                <option value=\"TCB\">Techcombank (TCB)</option>\n                <option value=\"VTB\">Vietinbank (VTB)</option>\n                <option value=\"BIDV\">BIDV</option>\n                <option value=\"ACB\">ACB</option>\n                <option value=\"MB\">MB Bank</option>\n                <option value=\"TPB\">TPBank</option>\n                <option value=\"STB\">Sacombank (STB)</option>\n              </select>\n              {errors.bankCode && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.bankCode.message}</p>\n              )}\n            </div>\n          </>\n        )}\n\n        {currency === 'USDT' && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Địa chỉ ví USDT (TRC20)\n            </label>\n            <input\n              type=\"text\"\n              {...register('walletAddress')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Nhập địa chỉ ví USDT\"\n            />\n            {errors.walletAddress && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.walletAddress.message}</p>\n            )}\n          </div>\n        )}\n\n        {/* Expiration */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Thời hạn\n          </label>\n          <select\n            {...register('expiresIn')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"3h\">3 giờ</option>\n            <option value=\"1d\">1 ngày</option>\n            <option value=\"3d\">3 ngày</option>\n            <option value=\"7d\">7 ngày</option>\n          </select>\n        </div>\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={isLoading}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? 'Đang tạo...' : 'Tạo Link Thanh Toán'}\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;AAIA;AANA;;;;;;;AAQA,oBAAoB;AACpB,MAAM,sBAAsB,EAAE,MAAM,CAAC;IACnC,QAAQ,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM;IAC7B,UAAU,EAAE,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE;QAAE,gBAAgB;IAA0B;IAC9E,eAAe,EAAE,MAAM,GAAG,QAAQ;IAClC,aAAa,EAAE,MAAM,GAAG,QAAQ;IAChC,UAAU,EAAE,MAAM,GAAG,QAAQ;IAC7B,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC;AAChC,GAAG,MAAM,CAAC,CAAC;IACT,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,aAAa,EAAE;QACnD,OAAO;IACT;IACA,IAAI,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,QAAQ,GAAG;QACpE,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAW;AACpB;AAQe,SAAS,kBAAkB,EAAE,SAAS,EAA0B;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,QAA+B;QACjC,UAAU,YAAY;QACtB,eAAe;YACb,UAAU;YACV,WAAW;QACb;IACF;IAEA,MAAM,WAAW,MAAM;IAEvB,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,cAAc,MAAM,iHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;YACvD,UAAU;YACV;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;YAErD,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACJ,GAAG,SAAS,UAAU;oCAAE,eAAe;gCAAK,EAAE;gCAC/C,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,MAAM,kBACZ,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;kCAKnE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;4BAEtB,OAAO,QAAQ,kBACd,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;oBAKpE,aAAa,uBACZ;;0CACE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACJ,GAAG,SAAS,cAAc;wCAC3B,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0CAIxE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACE,GAAG,SAAS,WAAW;wCACxB,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;oCAErB,OAAO,QAAQ,kBACd,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;oBAMxE,aAAa,wBACZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACJ,GAAG,SAAS,gBAAgB;gCAC7B,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,kBACnB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kCAM5E,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACE,GAAG,SAAS,YAAY;gCACzB,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/components/PaymentLinkResult.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport QRCode from 'qrcode';\nimport { PaymentLink, formatCurrency, formatDate, getTimeRemaining } from '@/lib/api';\nimport { Copy, Check, ExternalLink, Clock } from 'lucide-react';\n\ninterface PaymentLinkResultProps {\n  paymentLink: PaymentLink;\n  onCreateNew: () => void;\n}\n\nexport default function PaymentLinkResult({ paymentLink, onCreateNew }: PaymentLinkResultProps) {\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [copied, setCopied] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState<string>('');\n\n  const paymentUrl = `${window.location.origin}/pay/${paymentLink.hash}`;\n\n  useEffect(() => {\n    // Generate QR code\n    QRCode.toDataURL(paymentUrl, {\n      width: 256,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF',\n      },\n    })\n      .then(setQrCodeUrl)\n      .catch(console.error);\n  }, [paymentUrl]);\n\n  useEffect(() => {\n    // Update time remaining every minute\n    const updateTime = () => {\n      setTimeRemaining(getTimeRemaining(paymentLink.expiresAt));\n    };\n\n    updateTime();\n    const interval = setInterval(updateTime, 60000); // Update every minute\n\n    return () => clearInterval(interval);\n  }, [paymentLink.expiresAt]);\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(paymentUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  const openPaymentLink = () => {\n    window.open(paymentUrl, '_blank');\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n      <div className=\"text-center mb-6\">\n        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <Check className=\"w-8 h-8 text-green-600\" />\n        </div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Link Thanh Toán Đã Tạo!</h2>\n        <p className=\"text-gray-600\">Chia sẻ link này để nhận thanh toán</p>\n      </div>\n\n      {/* Payment Info */}\n      <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-gray-500\">Số tiền:</span>\n            <p className=\"font-semibold text-lg\">\n              {formatCurrency(paymentLink.amount, paymentLink.currency)}\n            </p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Loại tiền:</span>\n            <p className=\"font-semibold\">{paymentLink.currency}</p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Tạo lúc:</span>\n            <p className=\"font-semibold\">{formatDate(paymentLink.createdAt)}</p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Hết hạn:</span>\n            <p className=\"font-semibold flex items-center\">\n              <Clock className=\"w-4 h-4 mr-1\" />\n              {timeRemaining}\n            </p>\n          </div>\n        </div>\n\n        {/* Payment Method Info */}\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <span className=\"text-gray-500 text-sm\">Thông tin thanh toán:</span>\n          {paymentLink.currency === 'VND' ? (\n            <div className=\"mt-1\">\n              <p className=\"font-semibold\">STK: {paymentLink.bankAccount}</p>\n              <p className=\"text-sm text-gray-600\">Ngân hàng: {paymentLink.bankCode}</p>\n            </div>\n          ) : (\n            <div className=\"mt-1\">\n              <p className=\"font-semibold text-xs break-all\">{paymentLink.walletAddress}</p>\n              <p className=\"text-sm text-gray-600\">Mạng: TRC20</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* QR Code */}\n      {qrCodeUrl && (\n        <div className=\"text-center mb-6\">\n          <p className=\"text-sm text-gray-600 mb-3\">Quét mã QR để mở link:</p>\n          <div className=\"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg\">\n            <img src={qrCodeUrl} alt=\"QR Code\" className=\"w-48 h-48\" />\n          </div>\n        </div>\n      )}\n\n      {/* Payment Link */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Link thanh toán:\n        </label>\n        <div className=\"flex items-center space-x-2\">\n          <input\n            type=\"text\"\n            value={paymentUrl}\n            readOnly\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm\"\n          />\n          <button\n            onClick={copyToClipboard}\n            className=\"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            title=\"Sao chép link\"\n          >\n            {copied ? <Check className=\"w-4 h-4\" /> : <Copy className=\"w-4 h-4\" />}\n          </button>\n          <button\n            onClick={openPaymentLink}\n            className=\"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\"\n            title=\"Mở link\"\n          >\n            <ExternalLink className=\"w-4 h-4\" />\n          </button>\n        </div>\n        {copied && (\n          <p className=\"text-sm text-green-600 mt-1\">Đã sao chép vào clipboard!</p>\n        )}\n      </div>\n\n      {/* Actions */}\n      <div className=\"space-y-3\">\n        <button\n          onClick={onCreateNew}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          Tạo Link Mới\n        </button>\n        \n        <button\n          onClick={openPaymentLink}\n          className=\"w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n        >\n          Xem Trang Thanh Toán\n        </button>\n      </div>\n\n      {/* Warning */}\n      <div className=\"mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md\">\n        <p className=\"text-sm text-yellow-800\">\n          <strong>Lưu ý:</strong> Hãy lưu link này an toàn. Link sẽ hết hạn sau {timeRemaining}.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;;;;;;AAJA;;;;;;AAYe,SAAS,kBAAkB,EAAE,WAAW,EAAE,WAAW,EAA0B;IAC5F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,IAAI,EAAE;IAEtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,OAAO,SAAS,CAAC,YAAY;YAC3B,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF,GACG,IAAI,CAAC,cACL,KAAK,CAAC,QAAQ,KAAK;IACxB,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,aAAa;YACjB,iBAAiB,CAAA,GAAA,iHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,SAAS;QACzD;QAEA;QACA,MAAM,WAAW,YAAY,YAAY,QAAQ,sBAAsB;QAEvE,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,YAAY,SAAS;KAAC;IAE1B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;;;;;;;;;;kCAEnB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,EAAE,YAAY,QAAQ;;;;;;;;;;;;0CAG5D,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDAAiB,YAAY,QAAQ;;;;;;;;;;;;0CAEpD,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDAAiB,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,SAAS;;;;;;;;;;;;0CAEhE,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAM,WAAU;;;;;;4CAChB;;;;;;;;;;;;;;;;;;;kCAMP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;4BACvC,YAAY,QAAQ,KAAK,sBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAgB;4CAAM,YAAY,WAAW;;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAY,YAAY,QAAQ;;;;;;;;;;;;qDAGvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAmC,YAAY,aAAa;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;YAO5C,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAK;4BAAW,KAAI;4BAAU,WAAU;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEL,uBAAS,8OAAC;oCAAM,WAAU;;;;;yDAAe,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAE5D,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC;oCAAa,WAAU;;;;;;;;;;;;;;;;;oBAG3B,wBACC,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAe;wBAAgD;wBAAc;;;;;;;;;;;;;;;;;;AAK/F", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport CreatePaymentForm from '@/components/CreatePaymentForm';\nimport PaymentLinkResult from '@/components/PaymentLinkResult';\nimport { PaymentLink } from '@/lib/api';\n\nexport default function Home() {\n  const [createdPaymentLink, setCreatedPaymentLink] = useState<PaymentLink | null>(null);\n\n  const handlePaymentLinkCreated = (paymentLink: PaymentLink) => {\n    setCreatedPaymentLink(paymentLink);\n  };\n\n  const handleCreateNew = () => {\n    setCreatedPaymentLink(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 py-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Payment Link Generator\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Tạo link thanh toán nhanh chóng và an toàn\n          </p>\n        </div>\n\n        {createdPaymentLink ? (\n          <PaymentLinkResult\n            paymentLink={createdPaymentLink}\n            onCreateNew={handleCreateNew}\n          />\n        ) : (\n          <CreatePaymentForm onSuccess={handlePaymentLinkCreated} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,MAAM,2BAA2B,CAAC;QAChC,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,sBAAsB;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAKtC,mCACC,8OAAC,uIAAA,CAAA,UAAiB;oBAChB,aAAa;oBACb,aAAa;;;;;yCAGf,8OAAC,uIAAA,CAAA,UAAiB;oBAAC,WAAW;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}