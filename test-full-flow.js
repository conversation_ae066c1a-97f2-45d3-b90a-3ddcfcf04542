// Complete flow test for Payment Link system
const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testCompleteFlow() {
  console.log('🧪 Testing Complete Payment Link Flow...\n');

  try {
    // Step 1: Health check
    console.log('1️⃣ Health Check...');
    const health = await makeRequest('GET', '/api/health');
    console.log(`   ✅ Status: ${health.status} - ${health.data.status}`);

    // Step 2: Create VND payment link
    console.log('\n2️⃣ Creating VND Payment Link...');
    const vndPayment = {
      amount: 1500000,
      currency: 'VND',
      bankAccount: '**********',
      bankCode: 'TCB',
      expiresIn: '1d'
    };
    const vndResult = await makeRequest('POST', '/api/create-payment-link', vndPayment);
    console.log(`   ✅ Created: ${vndResult.data.data.hash}`);
    console.log(`   💰 Amount: ${vndResult.data.data.amount.toLocaleString()} VND`);
    console.log(`   🏦 Bank: ${vndResult.data.data.bankCode} - ${vndResult.data.data.bankAccount}`);
    
    const vndHash = vndResult.data.data.hash;
    const vndFrontendUrl = `http://localhost:3001/pay/${vndHash}`;

    // Step 3: Create USDT payment link
    console.log('\n3️⃣ Creating USDT Payment Link...');
    const usdtPayment = {
      amount: 75.50,
      currency: 'USDT',
      walletAddress: 'TRX_WALLET_ADDRESS_EXAMPLE_1234567890ABCDEF',
      expiresIn: '3h'
    };
    const usdtResult = await makeRequest('POST', '/api/create-payment-link', usdtPayment);
    console.log(`   ✅ Created: ${usdtResult.data.data.hash}`);
    console.log(`   💰 Amount: ${usdtResult.data.data.amount} USDT`);
    console.log(`   🔗 Wallet: ${usdtResult.data.data.walletAddress.substring(0, 20)}...`);
    
    const usdtHash = usdtResult.data.data.hash;
    const usdtFrontendUrl = `http://localhost:3001/pay/${usdtHash}`;

    // Step 4: Simulate payment viewing
    console.log('\n4️⃣ Simulating Payment Page Views...');
    const vndView = await makeRequest('GET', `/api/pay/${vndHash}`);
    console.log(`   👀 VND Link viewed - Status: ${vndView.data.data.isPaid ? 'Paid' : 'Pending'}`);
    
    const usdtView = await makeRequest('GET', `/api/pay/${usdtHash}`);
    console.log(`   👀 USDT Link viewed - Status: ${usdtView.data.data.isPaid ? 'Paid' : 'Pending'}`);

    // Step 5: Mark VND payment as paid
    console.log('\n5️⃣ Marking VND Payment as Paid...');
    const markPaid = await makeRequest('POST', `/api/payment/${vndHash}/mark-paid`, { confirmed: true });
    console.log(`   ✅ VND Payment confirmed at: ${markPaid.data.data.paidAt}`);

    // Step 6: Verify payment status
    console.log('\n6️⃣ Verifying Payment Status...');
    const finalVndCheck = await makeRequest('GET', `/api/pay/${vndHash}`);
    console.log(`   ✅ VND Status: ${finalVndCheck.data.data.isPaid ? '✅ PAID' : '⏳ PENDING'}`);
    
    const finalUsdtCheck = await makeRequest('GET', `/api/pay/${usdtHash}`);
    console.log(`   ⏳ USDT Status: ${finalUsdtCheck.data.data.isPaid ? '✅ PAID' : '⏳ PENDING'}`);

    // Step 7: Display frontend URLs
    console.log('\n🌐 Frontend URLs for Testing:');
    console.log(`   VND Payment (PAID):    ${vndFrontendUrl}`);
    console.log(`   USDT Payment (PENDING): ${usdtFrontendUrl}`);

    // Step 8: Test error cases
    console.log('\n7️⃣ Testing Error Cases...');
    
    // Invalid hash
    const invalidHash = await makeRequest('GET', '/api/pay/invalid-hash-123');
    console.log(`   ❌ Invalid hash: ${invalidHash.status} - ${invalidHash.data.message}`);
    
    // Invalid payment data
    const invalidPayment = await makeRequest('POST', '/api/create-payment-link', {
      amount: -100,
      currency: 'INVALID'
    });
    console.log(`   ❌ Invalid data: ${invalidPayment.status} - ${invalidPayment.data.message}`);

    // Try to mark already paid link as paid again
    const doublePaid = await makeRequest('POST', `/api/payment/${vndHash}/mark-paid`, { confirmed: true });
    console.log(`   ❌ Double payment: ${doublePaid.status} - ${doublePaid.data.message}`);

    console.log('\n🎉 Complete Flow Test Finished!');
    console.log('\n📊 Summary:');
    console.log(`   ✅ VND Payment Link: ${vndHash} (PAID)`);
    console.log(`   ⏳ USDT Payment Link: ${usdtHash} (PENDING)`);
    console.log(`   🌐 Frontend: http://localhost:3001`);
    console.log(`   📡 Backend: http://localhost:3000`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testCompleteFlow();
