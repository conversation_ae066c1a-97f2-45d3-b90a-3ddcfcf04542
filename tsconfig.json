{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "prisma/seed.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}