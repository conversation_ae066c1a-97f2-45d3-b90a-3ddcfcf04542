// Quick script to create a test payment link
const http = require('http');

const data = JSON.stringify({
  amount: 500000,
  currency: 'VND',
  bankAccount: '**********',
  bankCode: 'VCB',
  expiresIn: '1d'
});

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/create-payment-link',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(data)
  }
};

const req = http.request(options, (res) => {
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  res.on('end', () => {
    try {
      const response = JSON.parse(body);
      if (response.data && response.data.hash) {
        console.log('✅ Payment link created!');
        console.log('Hash:', response.data.hash);
        console.log('Frontend URL:', `http://localhost:3001/pay/${response.data.hash}`);
        console.log('Amount:', response.data.amount, response.data.currency);
      } else {
        console.log('❌ Error:', response);
      }
    } catch (e) {
      console.log('❌ Parse error:', e.message);
      console.log('Response:', body);
    }
  });
});

req.on('error', (err) => {
  console.error('❌ Request error:', err);
});

req.write(data);
req.end();
