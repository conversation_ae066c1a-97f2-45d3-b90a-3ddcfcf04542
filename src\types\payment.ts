export interface CreatePaymentLinkRequest {
  amount: number;
  currency: 'VND' | 'USDT';
  walletAddress?: string;
  bankAccount?: string;
  bankCode?: string;
  expiresIn?: string; // '3h', '1d', '3d', '7d'
}

export interface PaymentLinkResponse {
  id: number;
  hash: string;
  amount: number;
  currency: 'VND' | 'USDT';
  walletAddress?: string;
  bankAccount?: string;
  bankCode?: string;
  isPaid: boolean;
  paidAt?: Date;
  receiptUrl?: string;
  uploadedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
  isExpired: boolean;
}

export interface MarkPaidRequest {
  confirmed: boolean;
}

export interface UploadReceiptResponse {
  receiptUrl: string;
  uploadedAt: Date;
}
