import { Request, Response } from 'express';
import { PaymentLink } from '@prisma/client';
import prisma from '../lib/database';
import {
  generatePaymentHash,
  generateExpirationDate,
  isExpired,
  isValidAmount,
  createError,
  createSuccess
} from '../lib/utils';
import {
  CreatePaymentLinkRequest,
  PaymentLinkResponse,
  MarkPaidRequest
} from '../types/payment';

// Helper function to convert Prisma PaymentLink to PaymentLinkResponse
function toPaymentLinkResponse(paymentLink: PaymentLink): PaymentLinkResponse {
  return {
    id: paymentLink.id,
    hash: paymentLink.hash,
    amount: Number(paymentLink.amount),
    currency: paymentLink.currency as 'VND' | 'USDT',
    walletAddress: paymentLink.walletAddress || undefined,
    bankAccount: paymentLink.bankAccount || undefined,
    bankCode: paymentLink.bankCode || undefined,
    isPaid: paymentLink.isPaid,
    paidAt: paymentLink.paidAt || undefined,
    receiptUrl: paymentLink.receiptUrl || undefined,
    uploadedAt: paymentLink.uploadedAt || undefined,
    expiresAt: paymentLink.expiresAt || undefined,
    createdAt: paymentLink.createdAt,
    isExpired: isExpired(paymentLink.expiresAt)
  };
}

// Create new payment link
export async function createPaymentLink(req: Request, res: Response) {
  const { amount, currency, walletAddress, bankAccount, bankCode, expiresIn = '1d' }: CreatePaymentLinkRequest = req.body;

  // Validation
  if (!amount || !currency) {
    return res.status(400).json(createError('Amount and currency are required'));
  }

  if (!isValidAmount(amount, currency)) {
    return res.status(400).json(createError('Invalid amount for the specified currency'));
  }

  // Validate payment method
  if (currency === 'USDT' && !walletAddress) {
    return res.status(400).json(createError('Wallet address is required for USDT payments'));
  }

  if (currency === 'VND' && (!bankAccount || !bankCode)) {
    return res.status(400).json(createError('Bank account and bank code are required for VND payments'));
  }

  try {
    // Generate unique hash
    let hash: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 5;

    while (!isUnique && attempts < maxAttempts) {
      hash = generatePaymentHash();
      const existing = await prisma.paymentLink.findUnique({ where: { hash } });
      isUnique = !existing;
      attempts++;
    }

    if (!isUnique) {
      return res.status(500).json(createError('Failed to generate unique hash'));
    }

    // Create payment link
    const paymentLink = await prisma.paymentLink.create({
      data: {
        hash: hash!,
        amount,
        currency,
        walletAddress: currency === 'USDT' ? walletAddress : null,
        bankAccount: currency === 'VND' ? bankAccount : null,
        bankCode: currency === 'VND' ? bankCode : null,
        expiresAt: generateExpirationDate(expiresIn)
      }
    });

    // Log creation
    await prisma.paymentLog.create({
      data: {
        paymentLinkId: paymentLink.id,
        action: 'created',
        details: JSON.stringify({ currency, amount }),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    const response = toPaymentLinkResponse(paymentLink);
    return res.status(201).json(createSuccess(response, 'Payment link created successfully'));
  } catch (error) {
    console.error('Error creating payment link:', error);
    return res.status(500).json(createError('Failed to create payment link'));
  }
}

// Get payment link by hash
export async function getPaymentLink(req: Request, res: Response) {
  const { hash } = req.params;

  if (!hash || hash.length !== 32) {
    return res.status(400).json(createError('Invalid payment link hash'));
  }

  try {
    const paymentLink = await prisma.paymentLink.findUnique({
      where: { hash }
    });

    if (!paymentLink) {
      return res.status(404).json(createError('Payment link not found'));
    }

    // Log view
    await prisma.paymentLog.create({
      data: {
        paymentLinkId: paymentLink.id,
        action: 'viewed',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    const response = toPaymentLinkResponse(paymentLink);
    return res.json(createSuccess(response, 'Payment link retrieved successfully'));
  } catch (error) {
    console.error('Error getting payment link:', error);
    return res.status(500).json(createError('Failed to retrieve payment link'));
  }
}

// Mark payment as paid
export async function markAsPaid(req: Request, res: Response) {
  const { hash } = req.params;
  const { confirmed }: MarkPaidRequest = req.body;

  if (!hash || hash.length !== 32) {
    return res.status(400).json(createError('Invalid payment link hash'));
  }

  if (typeof confirmed !== 'boolean') {
    return res.status(400).json(createError('Confirmed field must be a boolean'));
  }

  try {
    const paymentLink = await prisma.paymentLink.findUnique({
      where: { hash }
    });

    if (!paymentLink) {
      return res.status(404).json(createError('Payment link not found'));
    }

    if (isExpired(paymentLink.expiresAt)) {
      return res.status(400).json(createError('Payment link has expired'));
    }

    if (paymentLink.isPaid) {
      return res.status(400).json(createError('Payment already confirmed'));
    }

    // Update payment status
    const updatedLink = await prisma.paymentLink.update({
      where: { hash },
      data: {
        isPaid: confirmed,
        paidAt: confirmed ? new Date() : null
      }
    });

    // Log payment confirmation
    await prisma.paymentLog.create({
      data: {
        paymentLinkId: paymentLink.id,
        action: confirmed ? 'paid' : 'unpaid',
        details: JSON.stringify({ confirmed }),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    const response = toPaymentLinkResponse(updatedLink);
    return res.json(createSuccess(response, `Payment ${confirmed ? 'confirmed' : 'unconfirmed'} successfully`));
  } catch (error) {
    console.error('Error marking payment as paid:', error);
    return res.status(500).json(createError('Failed to update payment status'));
  }
}
