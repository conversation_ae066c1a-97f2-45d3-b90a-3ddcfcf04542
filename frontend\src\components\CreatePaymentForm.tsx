'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { paymentAPI, CreatePaymentLinkRequest, PaymentLink } from '@/lib/api';

// Validation schema
const createPaymentSchema = z.object({
  amount: z.number().min(0.01, 'Số tiền phải lớn hơn 0'),
  currency: z.enum(['VND', 'USDT'], { required_error: 'Vui lòng chọn loại tiền' }),
  walletAddress: z.string().optional(),
  bankAccount: z.string().optional(),
  bankCode: z.string().optional(),
  expiresIn: z.string().default('1d'),
}).refine((data) => {
  if (data.currency === 'USDT' && !data.walletAddress) {
    return false;
  }
  if (data.currency === 'VND' && (!data.bankAccount || !data.bankCode)) {
    return false;
  }
  return true;
}, {
  message: '<PERSON>ui lòng điền đầy đủ thông tin thanh toán',
  path: ['currency'],
});

type CreatePaymentFormData = z.infer<typeof createPaymentSchema>;

interface CreatePaymentFormProps {
  onSuccess: (paymentLink: PaymentLink) => void;
}

export default function CreatePaymentForm({ onSuccess }: CreatePaymentFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
  } = useForm<CreatePaymentFormData>({
    resolver: zodResolver(createPaymentSchema),
    defaultValues: {
      currency: 'VND',
      expiresIn: '1d',
    },
  });

  const currency = watch('currency');

  const onSubmit = async (data: CreatePaymentFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const paymentLink = await paymentAPI.createPaymentLink(data as CreatePaymentLinkRequest);
      onSuccess(paymentLink);
      reset();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Tạo Link Thanh Toán</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Số tiền
          </label>
          <input
            type="number"
            step="0.01"
            {...register('amount', { valueAsNumber: true })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Nhập số tiền"
          />
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
          )}
        </div>

        {/* Currency */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Loại tiền
          </label>
          <select
            {...register('currency')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="VND">VND (Việt Nam Đồng)</option>
            <option value="USDT">USDT (Tether)</option>
          </select>
          {errors.currency && (
            <p className="mt-1 text-sm text-red-600">{errors.currency.message}</p>
          )}
        </div>

        {/* Payment Method Fields */}
        {currency === 'VND' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Số tài khoản ngân hàng
              </label>
              <input
                type="text"
                {...register('bankAccount')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nhập số tài khoản"
              />
              {errors.bankAccount && (
                <p className="mt-1 text-sm text-red-600">{errors.bankAccount.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mã ngân hàng
              </label>
              <select
                {...register('bankCode')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Chọn ngân hàng</option>
                <option value="VCB">Vietcombank (VCB)</option>
                <option value="TCB">Techcombank (TCB)</option>
                <option value="VTB">Vietinbank (VTB)</option>
                <option value="BIDV">BIDV</option>
                <option value="ACB">ACB</option>
                <option value="MB">MB Bank</option>
                <option value="TPB">TPBank</option>
                <option value="STB">Sacombank (STB)</option>
              </select>
              {errors.bankCode && (
                <p className="mt-1 text-sm text-red-600">{errors.bankCode.message}</p>
              )}
            </div>
          </>
        )}

        {currency === 'USDT' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Địa chỉ ví USDT (TRC20)
            </label>
            <input
              type="text"
              {...register('walletAddress')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nhập địa chỉ ví USDT"
            />
            {errors.walletAddress && (
              <p className="mt-1 text-sm text-red-600">{errors.walletAddress.message}</p>
            )}
          </div>
        )}

        {/* Expiration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Thời hạn
          </label>
          <select
            {...register('expiresIn')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="3h">3 giờ</option>
            <option value="1d">1 ngày</option>
            <option value="3d">3 ngày</option>
            <option value="7d">7 ngày</option>
          </select>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Đang tạo...' : 'Tạo Link Thanh Toán'}
        </button>
      </form>
    </div>
  );
}
