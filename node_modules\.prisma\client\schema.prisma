// Payment Link System - Prisma Schema
// MySQL database configuration for payment link management

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Main payment links model
model PaymentLink {
  id       Int      @id @default(autoincrement())
  hash     String   @unique
  amount   Decimal
  currency Currency

  // Payment destination (either crypto or bank)
  walletAddress String? @map("wallet_address")
  bankAccount   String? @map("bank_account")
  bankCode      String? @map("bank_code")

  // Payment status
  isPaid Boolean   @default(false) @map("is_paid")
  paidAt DateTime? @map("paid_at")

  // Receipt management
  receiptUrl String?   @map("receipt_url")
  uploadedAt DateTime? @map("uploaded_at")

  // Expiration
  expiresAt DateTime? @map("expires_at")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  logs PaymentLog[]

  @@index([hash])
  @@index([createdAt])
  @@index([expiresAt])
  @@index([isPaid])
  @@map("payment_links")
}

// Payment activity logs
model PaymentLog {
  id            Int      @id @default(autoincrement())
  paymentLinkId Int      @map("payment_link_id")
  action        String
  details       String? // JSON as string for SQLite compatibility
  ipAddress     String?  @map("ip_address")
  userAgent     String?  @map("user_agent")
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations
  paymentLink PaymentLink @relation(fields: [paymentLinkId], references: [id], onDelete: Cascade)

  @@index([paymentLinkId])
  @@index([action])
  @@index([createdAt])
  @@map("payment_logs")
}

// Enums
enum Currency {
  VND
  USDT
}
