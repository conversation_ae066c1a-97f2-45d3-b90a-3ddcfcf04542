import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface CreatePaymentLinkRequest {
  amount: number;
  currency: 'VND' | 'USDT';
  walletAddress?: string;
  bankAccount?: string;
  bankCode?: string;
  expiresIn?: string;
}

export interface PaymentLink {
  id: number;
  hash: string;
  amount: number;
  currency: 'VND' | 'USDT';
  walletAddress?: string;
  bankAccount?: string;
  bankCode?: string;
  isPaid: boolean;
  paidAt?: string;
  receiptUrl?: string;
  uploadedAt?: string;
  expiresAt?: string;
  createdAt: string;
  isExpired: boolean;
}

export interface ApiResponse<T> {
  error: boolean;
  message: string;
  data?: T;
  timestamp: string;
}

// API functions
export const paymentAPI = {
  // Create payment link
  async createPaymentLink(data: CreatePaymentLinkRequest): Promise<PaymentLink> {
    const response = await api.post<ApiResponse<PaymentLink>>('/create-payment-link', data);
    if (response.data.error) {
      throw new Error(response.data.message);
    }
    return response.data.data!;
  },

  // Get payment link by hash
  async getPaymentLink(hash: string): Promise<PaymentLink> {
    const response = await api.get<ApiResponse<PaymentLink>>(`/pay/${hash}`);
    if (response.data.error) {
      throw new Error(response.data.message);
    }
    return response.data.data!;
  },

  // Mark payment as paid
  async markAsPaid(hash: string, confirmed: boolean): Promise<PaymentLink> {
    const response = await api.post<ApiResponse<PaymentLink>>(`/payment/${hash}/mark-paid`, { confirmed });
    if (response.data.error) {
      throw new Error(response.data.message);
    }
    return response.data.data!;
  },

  // Upload receipt
  async uploadReceipt(hash: string, file: File): Promise<{ receiptUrl: string; uploadedAt: string }> {
    const formData = new FormData();
    formData.append('receipt', file);
    
    const response = await api.post<ApiResponse<{ receiptUrl: string; uploadedAt: string }>>(
      `/payment/${hash}/upload-proof`, 
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    if (response.data.error) {
      throw new Error(response.data.message);
    }
    return response.data.data!;
  },

  // Health check
  async healthCheck(): Promise<{ status: string; database: string; timestamp: string }> {
    const response = await api.get('/health');
    return response.data;
  },
};

// Utility functions
export function formatCurrency(amount: number, currency: 'VND' | 'USDT'): string {
  if (currency === 'VND') {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  } else {
    return `${amount.toFixed(2)} USDT`;
  }
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function isExpired(expiresAt?: string): boolean {
  if (!expiresAt) return false;
  return new Date() > new Date(expiresAt);
}

export function getTimeRemaining(expiresAt?: string): string {
  if (!expiresAt) return '';
  
  const now = new Date().getTime();
  const expiry = new Date(expiresAt).getTime();
  const diff = expiry - now;
  
  if (diff <= 0) return 'Đã hết hạn';
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}
