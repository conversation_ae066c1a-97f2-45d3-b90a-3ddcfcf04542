{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types\nexport interface CreatePaymentLinkRequest {\n  amount: number;\n  currency: 'VND' | 'USDT';\n  walletAddress?: string;\n  bankAccount?: string;\n  bankCode?: string;\n  expiresIn?: string;\n}\n\nexport interface PaymentLink {\n  id: number;\n  hash: string;\n  amount: number;\n  currency: 'VND' | 'USDT';\n  walletAddress?: string;\n  bankAccount?: string;\n  bankCode?: string;\n  isPaid: boolean;\n  paidAt?: string;\n  receiptUrl?: string;\n  uploadedAt?: string;\n  expiresAt?: string;\n  createdAt: string;\n  isExpired: boolean;\n}\n\nexport interface ApiResponse<T> {\n  error: boolean;\n  message: string;\n  data?: T;\n  timestamp: string;\n}\n\n// API functions\nexport const paymentAPI = {\n  // Create payment link\n  async createPaymentLink(data: CreatePaymentLinkRequest): Promise<PaymentLink> {\n    const response = await api.post<ApiResponse<PaymentLink>>('/create-payment-link', data);\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Get payment link by hash\n  async getPaymentLink(hash: string): Promise<PaymentLink> {\n    const response = await api.get<ApiResponse<PaymentLink>>(`/pay/${hash}`);\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Mark payment as paid\n  async markAsPaid(hash: string, confirmed: boolean): Promise<PaymentLink> {\n    const response = await api.post<ApiResponse<PaymentLink>>(`/payment/${hash}/mark-paid`, { confirmed });\n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Upload receipt\n  async uploadReceipt(hash: string, file: File): Promise<{ receiptUrl: string; uploadedAt: string }> {\n    const formData = new FormData();\n    formData.append('receipt', file);\n    \n    const response = await api.post<ApiResponse<{ receiptUrl: string; uploadedAt: string }>>(\n      `/payment/${hash}/upload-proof`, \n      formData,\n      {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      }\n    );\n    \n    if (response.data.error) {\n      throw new Error(response.data.message);\n    }\n    return response.data.data!;\n  },\n\n  // Health check\n  async healthCheck(): Promise<{ status: string; database: string; timestamp: string }> {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\n// Utility functions\nexport function formatCurrency(amount: number, currency: 'VND' | 'USDT'): string {\n  if (currency === 'VND') {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(amount);\n  } else {\n    return `${amount.toFixed(2)} USDT`;\n  }\n}\n\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleString('vi-VN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function isExpired(expiresAt?: string): boolean {\n  if (!expiresAt) return false;\n  return new Date() > new Date(expiresAt);\n}\n\nexport function getTimeRemaining(expiresAt?: string): string {\n  if (!expiresAt) return '';\n  \n  const now = new Date().getTime();\n  const expiry = new Date(expiresAt).getTime();\n  const diff = expiry - now;\n  \n  if (diff <= 0) return 'Đã hết hạn';\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  \n  if (hours > 0) {\n    return `${hours}h ${minutes}m`;\n  } else {\n    return `${minutes}m`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEqB;;;;;;;AAArB,MAAM,eAAe,iEAAmC;AAExD,MAAM,MAAM,MAAM,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAqCO,MAAM,aAAa;IACxB,sBAAsB;IACtB,MAAM,mBAAkB,IAA8B;QACpD,MAAM,WAAW,MAAM,IAAI,IAAI,CAA2B,wBAAwB;QAClF,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,2BAA2B;IAC3B,MAAM,gBAAe,IAAY;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAA2B,CAAC,KAAK,EAAE,MAAM;QACvE,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,uBAAuB;IACvB,MAAM,YAAW,IAAY,EAAE,SAAkB;QAC/C,MAAM,WAAW,MAAM,IAAI,IAAI,CAA2B,CAAC,SAAS,EAAE,KAAK,UAAU,CAAC,EAAE;YAAE;QAAU;QACpG,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,iBAAiB;IACjB,MAAM,eAAc,IAAY,EAAE,IAAU;QAC1C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,WAAW;QAE3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAC7B,CAAC,SAAS,EAAE,KAAK,aAAa,CAAC,EAC/B,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAGF,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;QACvC;QACA,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B;IAEA,eAAe;IACf,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,SAAS,eAAe,MAAc,EAAE,QAAwB;IACrE,IAAI,aAAa,OAAO;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ,OAAO;QACL,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC;IACpC;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;QAClD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,UAAU,SAAkB;IAC1C,IAAI,CAAC,WAAW,OAAO;IACvB,OAAO,IAAI,SAAS,IAAI,KAAK;AAC/B;AAEO,SAAS,iBAAiB,SAAkB;IACjD,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,MAAM,IAAI,OAAO,OAAO;IAC9B,MAAM,SAAS,IAAI,KAAK,WAAW,OAAO;IAC1C,MAAM,OAAO,SAAS;IAEtB,IAAI,QAAQ,GAAG,OAAO;IAEtB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IAEjE,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;IAChC,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/components/CreatePaymentForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { paymentAPI, CreatePaymentLinkRequest, PaymentLink } from '@/lib/api';\n\n// Validation schema\nconst createPaymentSchema = z.object({\n  amount: z.number().min(0.01, 'Số tiền phải lớn hơn 0'),\n  currency: z.enum(['VND', 'USDT'], { required_error: 'Vui lòng chọn loại tiền' }),\n  walletAddress: z.string().optional(),\n  bankAccount: z.string().optional(),\n  bankCode: z.string().optional(),\n  expiresIn: z.string().default('1d'),\n}).refine((data) => {\n  if (data.currency === 'USDT' && !data.walletAddress) {\n    return false;\n  }\n  if (data.currency === 'VND' && (!data.bankAccount || !data.bankCode)) {\n    return false;\n  }\n  return true;\n}, {\n  message: '<PERSON>ui lòng điền đầy đủ thông tin thanh toán',\n  path: ['currency'],\n});\n\ntype CreatePaymentFormData = z.infer<typeof createPaymentSchema>;\n\ninterface CreatePaymentFormProps {\n  onSuccess: (paymentLink: PaymentLink) => void;\n}\n\nexport default function CreatePaymentForm({ onSuccess }: CreatePaymentFormProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors },\n    reset,\n  } = useForm<CreatePaymentFormData>({\n    resolver: zodResolver(createPaymentSchema),\n    defaultValues: {\n      currency: 'VND',\n      expiresIn: '1d',\n    },\n  });\n\n  const currency = watch('currency');\n\n  const onSubmit = async (data: CreatePaymentFormData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const paymentLink = await paymentAPI.createPaymentLink(data as CreatePaymentLinkRequest);\n      onSuccess(paymentLink);\n      reset();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Tạo Link Thanh Toán</h2>\n      \n      {error && (\n        <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n        {/* Amount */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Số tiền\n          </label>\n          <input\n            type=\"number\"\n            step=\"0.01\"\n            {...register('amount', { valueAsNumber: true })}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Nhập số tiền\"\n          />\n          {errors.amount && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.amount.message}</p>\n          )}\n        </div>\n\n        {/* Currency */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Loại tiền\n          </label>\n          <select\n            {...register('currency')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"VND\">VND (Việt Nam Đồng)</option>\n            <option value=\"USDT\">USDT (Tether)</option>\n          </select>\n          {errors.currency && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.currency.message}</p>\n          )}\n        </div>\n\n        {/* Payment Method Fields */}\n        {currency === 'VND' && (\n          <>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Số tài khoản ngân hàng\n              </label>\n              <input\n                type=\"text\"\n                {...register('bankAccount')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Nhập số tài khoản\"\n              />\n              {errors.bankAccount && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.bankAccount.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Mã ngân hàng\n              </label>\n              <select\n                {...register('bankCode')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">Chọn ngân hàng</option>\n                <option value=\"VCB\">Vietcombank (VCB)</option>\n                <option value=\"TCB\">Techcombank (TCB)</option>\n                <option value=\"VTB\">Vietinbank (VTB)</option>\n                <option value=\"BIDV\">BIDV</option>\n                <option value=\"ACB\">ACB</option>\n                <option value=\"MB\">MB Bank</option>\n                <option value=\"TPB\">TPBank</option>\n                <option value=\"STB\">Sacombank (STB)</option>\n              </select>\n              {errors.bankCode && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.bankCode.message}</p>\n              )}\n            </div>\n          </>\n        )}\n\n        {currency === 'USDT' && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Địa chỉ ví USDT (TRC20)\n            </label>\n            <input\n              type=\"text\"\n              {...register('walletAddress')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Nhập địa chỉ ví USDT\"\n            />\n            {errors.walletAddress && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.walletAddress.message}</p>\n            )}\n          </div>\n        )}\n\n        {/* Expiration */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Thời hạn\n          </label>\n          <select\n            {...register('expiresIn')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"3h\">3 giờ</option>\n            <option value=\"1d\">1 ngày</option>\n            <option value=\"3d\">3 ngày</option>\n            <option value=\"7d\">7 ngày</option>\n          </select>\n        </div>\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={isLoading}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? 'Đang tạo...' : 'Tạo Link Thanh Toán'}\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;AAIA;;;AANA;;;;;;AAQA,oBAAoB;AACpB,MAAM,sBAAsB,EAAE,MAAM,CAAC;IACnC,QAAQ,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM;IAC7B,UAAU,EAAE,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE;QAAE,gBAAgB;IAA0B;IAC9E,eAAe,EAAE,MAAM,GAAG,QAAQ;IAClC,aAAa,EAAE,MAAM,GAAG,QAAQ;IAChC,UAAU,EAAE,MAAM,GAAG,QAAQ;IAC7B,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC;AAChC,GAAG,MAAM,CAAC,CAAC;IACT,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,aAAa,EAAE;QACnD,OAAO;IACT;IACA,IAAI,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,QAAQ,GAAG;QACpE,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAW;AACpB;AAQe,SAAS,kBAAkB,EAAE,SAAS,EAA0B;;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,QAA+B;QACjC,UAAU,YAAY;QACtB,eAAe;YACb,UAAU;YACV,WAAW;QACb;IACF;IAEA,MAAM,WAAW,MAAM;IAEvB,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,cAAc,MAAM,oHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;YACvD,UAAU;YACV;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;YAErD,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACJ,GAAG,SAAS,UAAU;oCAAE,eAAe;gCAAK,EAAE;gCAC/C,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,MAAM,kBACZ,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;kCAKnE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;4BAEtB,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;oBAKpE,aAAa,uBACZ;;0CACE,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACJ,GAAG,SAAS,cAAc;wCAC3B,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,WAAW,kBACjB,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0CAIxE,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACE,GAAG,SAAS,WAAW;wCACxB,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;oCAErB,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;oBAMxE,aAAa,wBACZ,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACJ,GAAG,SAAS,gBAAgB;gCAC7B,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,kBACnB,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kCAM5E,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACE,GAAG,SAAS,YAAY;gCACzB,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;AAKvC;GAvKwB;;QAUlB;;;KAVkB", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/components/PaymentLinkResult.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport QRCode from 'qrcode';\nimport { PaymentLink, formatCurrency, formatDate, getTimeRemaining } from '@/lib/api';\nimport { Copy, Check, ExternalLink, Clock } from 'lucide-react';\n\ninterface PaymentLinkResultProps {\n  paymentLink: PaymentLink;\n  onCreateNew: () => void;\n}\n\nexport default function PaymentLinkResult({ paymentLink, onCreateNew }: PaymentLinkResultProps) {\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [copied, setCopied] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState<string>('');\n\n  const paymentUrl = `${window.location.origin}/pay/${paymentLink.hash}`;\n\n  useEffect(() => {\n    // Generate QR code\n    QRCode.toDataURL(paymentUrl, {\n      width: 256,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF',\n      },\n    })\n      .then(setQrCodeUrl)\n      .catch(console.error);\n  }, [paymentUrl]);\n\n  useEffect(() => {\n    // Update time remaining every minute\n    const updateTime = () => {\n      setTimeRemaining(getTimeRemaining(paymentLink.expiresAt));\n    };\n\n    updateTime();\n    const interval = setInterval(updateTime, 60000); // Update every minute\n\n    return () => clearInterval(interval);\n  }, [paymentLink.expiresAt]);\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(paymentUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  const openPaymentLink = () => {\n    window.open(paymentUrl, '_blank');\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\">\n      <div className=\"text-center mb-6\">\n        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <Check className=\"w-8 h-8 text-green-600\" />\n        </div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Link Thanh Toán Đã Tạo!</h2>\n        <p className=\"text-gray-600\">Chia sẻ link này để nhận thanh toán</p>\n      </div>\n\n      {/* Payment Info */}\n      <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-gray-500\">Số tiền:</span>\n            <p className=\"font-semibold text-lg\">\n              {formatCurrency(paymentLink.amount, paymentLink.currency)}\n            </p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Loại tiền:</span>\n            <p className=\"font-semibold\">{paymentLink.currency}</p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Tạo lúc:</span>\n            <p className=\"font-semibold\">{formatDate(paymentLink.createdAt)}</p>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Hết hạn:</span>\n            <p className=\"font-semibold flex items-center\">\n              <Clock className=\"w-4 h-4 mr-1\" />\n              {timeRemaining}\n            </p>\n          </div>\n        </div>\n\n        {/* Payment Method Info */}\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <span className=\"text-gray-500 text-sm\">Thông tin thanh toán:</span>\n          {paymentLink.currency === 'VND' ? (\n            <div className=\"mt-1\">\n              <p className=\"font-semibold\">STK: {paymentLink.bankAccount}</p>\n              <p className=\"text-sm text-gray-600\">Ngân hàng: {paymentLink.bankCode}</p>\n            </div>\n          ) : (\n            <div className=\"mt-1\">\n              <p className=\"font-semibold text-xs break-all\">{paymentLink.walletAddress}</p>\n              <p className=\"text-sm text-gray-600\">Mạng: TRC20</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* QR Code */}\n      {qrCodeUrl && (\n        <div className=\"text-center mb-6\">\n          <p className=\"text-sm text-gray-600 mb-3\">Quét mã QR để mở link:</p>\n          <div className=\"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg\">\n            <img src={qrCodeUrl} alt=\"QR Code\" className=\"w-48 h-48\" />\n          </div>\n        </div>\n      )}\n\n      {/* Payment Link */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Link thanh toán:\n        </label>\n        <div className=\"flex items-center space-x-2\">\n          <input\n            type=\"text\"\n            value={paymentUrl}\n            readOnly\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm\"\n          />\n          <button\n            onClick={copyToClipboard}\n            className=\"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            title=\"Sao chép link\"\n          >\n            {copied ? <Check className=\"w-4 h-4\" /> : <Copy className=\"w-4 h-4\" />}\n          </button>\n          <button\n            onClick={openPaymentLink}\n            className=\"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\"\n            title=\"Mở link\"\n          >\n            <ExternalLink className=\"w-4 h-4\" />\n          </button>\n        </div>\n        {copied && (\n          <p className=\"text-sm text-green-600 mt-1\">Đã sao chép vào clipboard!</p>\n        )}\n      </div>\n\n      {/* Actions */}\n      <div className=\"space-y-3\">\n        <button\n          onClick={onCreateNew}\n          className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          Tạo Link Mới\n        </button>\n        \n        <button\n          onClick={openPaymentLink}\n          className=\"w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n        >\n          Xem Trang Thanh Toán\n        </button>\n      </div>\n\n      {/* Warning */}\n      <div className=\"mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md\">\n        <p className=\"text-sm text-yellow-800\">\n          <strong>Lưu ý:</strong> Hãy lưu link này an toàn. Link sẽ hết hạn sau {timeRemaining}.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;;;;;;;;AAJA;;;;;AAYe,SAAS,kBAAkB,EAAE,WAAW,EAAE,WAAW,EAA0B;;IAC5F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,IAAI,EAAE;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,mBAAmB;YACnB,OAAO,SAAS,CAAC,YAAY;gBAC3B,OAAO;gBACP,QAAQ;gBACR,OAAO;oBACL,MAAM;oBACN,OAAO;gBACT;YACF,GACG,IAAI,CAAC,cACL,KAAK,CAAC,QAAQ,KAAK;QACxB;sCAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,qCAAqC;YACrC,MAAM;0DAAa;oBACjB,iBAAiB,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,SAAS;gBACzD;;YAEA;YACA,MAAM,WAAW,YAAY,YAAY,QAAQ,sBAAsB;YAEvE;+CAAO,IAAM,cAAc;;QAC7B;sCAAG;QAAC,YAAY,SAAS;KAAC;IAE1B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,EAAE,YAAY,QAAQ;;;;;;;;;;;;0CAG5D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDAAiB,YAAY,QAAQ;;;;;;;;;;;;0CAEpD,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDAAiB,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,SAAS;;;;;;;;;;;;0CAEhE,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAM,WAAU;;;;;;4CAChB;;;;;;;;;;;;;;;;;;;kCAMP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;4BACvC,YAAY,QAAQ,KAAK,sBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgB;4CAAM,YAAY,WAAW;;;;;;;kDAC1D,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAY,YAAY,QAAQ;;;;;;;;;;;;qDAGvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAmC,YAAY,aAAa;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;YAO5C,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,KAAK;4BAAW,KAAI;4BAAU,WAAU;;;;;;;;;;;;;;;;;0BAMnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEL,uBAAS,6LAAC;oCAAM,WAAU;;;;;yDAAe,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAE5D,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCAAa,WAAU;;;;;;;;;;;;;;;;;oBAG3B,wBACC,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAK/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;sCAAO;;;;;;wBAAe;wBAAgD;wBAAc;;;;;;;;;;;;;;;;;;AAK/F;GAvKwB;KAAA", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport CreatePaymentForm from '@/components/CreatePaymentForm';\nimport PaymentLinkResult from '@/components/PaymentLinkResult';\nimport { PaymentLink } from '@/lib/api';\n\nexport default function Home() {\n  const [createdPaymentLink, setCreatedPaymentLink] = useState<PaymentLink | null>(null);\n\n  const handlePaymentLinkCreated = (paymentLink: PaymentLink) => {\n    setCreatedPaymentLink(paymentLink);\n  };\n\n  const handleCreateNew = () => {\n    setCreatedPaymentLink(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 py-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Payment Link Generator\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Tạo link thanh toán nhanh chóng và an toàn\n          </p>\n        </div>\n\n        {createdPaymentLink ? (\n          <PaymentLinkResult\n            paymentLink={createdPaymentLink}\n            onCreateNew={handleCreateNew}\n          />\n        ) : (\n          <CreatePaymentForm onSuccess={handlePaymentLinkCreated} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,MAAM,2BAA2B,CAAC;QAChC,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAKtC,mCACC,6LAAC,0IAAA,CAAA,UAAiB;oBAChB,aAAa;oBACb,aAAa;;;;;yCAGf,6LAAC,0IAAA,CAAA,UAAiB;oBAAC,WAAW;;;;;;;;;;;;;;;;;AAKxC;GAlCwB;KAAA", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/PaymenLink/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}