'use client';

import { useState } from 'react';
import CreatePaymentForm from '@/components/CreatePaymentForm';
import PaymentLinkResult from '@/components/PaymentLinkResult';
import { PaymentLink } from '@/lib/api';

export default function Home() {
  const [createdPaymentLink, setCreatedPaymentLink] = useState<PaymentLink | null>(null);

  const handlePaymentLinkCreated = (paymentLink: PaymentLink) => {
    setCreatedPaymentLink(paymentLink);
  };

  const handleCreateNew = () => {
    setCreatedPaymentLink(null);
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Payment Link Generator
          </h1>
          <p className="text-lg text-gray-600">
            Tạo link thanh toán nhanh chóng và an toàn
          </p>
        </div>

        {createdPaymentLink ? (
          <PaymentLinkResult
            paymentLink={createdPaymentLink}
            onCreateNew={handleCreateNew}
          />
        ) : (
          <CreatePaymentForm onSuccess={handlePaymentLinkCreated} />
        )}
      </div>
    </div>
  );
}
