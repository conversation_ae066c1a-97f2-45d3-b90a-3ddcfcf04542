{"name": "paymenlink", "version": "1.0.0", "description": "Payment Link Generation System with MySQL and Prisma", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["payment", "link", "mysql", "prisma", "express"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.10.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "prisma": "^6.10.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}