{"name": "paymenlink", "version": "1.0.0", "description": "Payment Link Generation System with MySQL and Prisma", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dev:simple": "npx ts-node src/test-server.ts", "dev:full": "node start-dev.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "test": "node test-api.js", "test:flow": "node test-full-flow.js", "test:create": "node create-test-link.js", "setup": "npm install && npm run db:generate && npm run db:push && npm run db:seed"}, "keywords": ["payment", "link", "mysql", "prisma", "express"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "multer": "^2.0.1", "prisma": "^6.10.1", "qrcode": "^1.5.4", "react-hook-form": "^7.58.1", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "@types/qrcode": "^1.5.5", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}