import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import {
  createPaymentLink,
  getPaymentLink,
  markAsPaid
} from '../controllers/paymentController';
import {
  uploadReceipt,
  getReceipt,
  upload
} from '../controllers/uploadController';

const router = Router();

// Payment link routes
router.post('/create-payment-link', asyncHandler(createPaymentLink));
router.get('/pay/:hash', asyncHandler(getPaymentLink));
router.post('/payment/:hash/mark-paid', asyncHandler(markAsPaid));

// File upload routes
// router.post('/payment/:hash/upload-proof', upload.single('receipt'), asyncHandler(uploadReceipt));
// router.get('/payment/:hash/proof', asyncHandler(getReceipt));

export default router;
