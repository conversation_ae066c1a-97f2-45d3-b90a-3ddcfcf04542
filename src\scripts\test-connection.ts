import dotenv from 'dotenv';
import { testDatabaseConnection, disconnectDatabase, healthCheck } from '../lib/database';

// Load environment variables
dotenv.config();

async function main() {
  console.log('🔍 Testing database connection...');
  console.log('Database URL:', process.env.DATABASE_URL?.replace(/password=[^&]+/, 'password=***'));
  
  try {
    // Test basic connection
    const isConnected = await testDatabaseConnection();
    
    if (!isConnected) {
      console.error('❌ Failed to connect to database');
      process.exit(1);
    }
    
    // Test health check
    console.log('🏥 Running health check...');
    const health = await healthCheck();
    console.log('✅ Health check passed:', health);
    
    console.log('🎉 All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    process.exit(1);
  } finally {
    await disconnectDatabase();
  }
}

// Run the test
main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
