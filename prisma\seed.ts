import { PrismaClient } from '@prisma/client';
import { generatePaymentHash, generateExpirationDate } from '../src/lib/utils';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create sample VND payment link
  const vndLink = await prisma.paymentLink.create({
    data: {
      hash: generatePaymentHash(),
      amount: 1000000, // 1,000,000 VND
      currency: 'VND',
      bankAccount: '**********',
      bankCode: 'VCB',
      expiresAt: generateExpirationDate('1d'),
    },
  });

  console.log('✅ Created VND payment link:', vndLink.hash);

  // Create sample USDT payment link
  const usdtLink = await prisma.paymentLink.create({
    data: {
      hash: generatePaymentHash(),
      amount: 50.00, // 50 USDT
      currency: 'USDT',
      walletAddress: 'TRX_SAMPLE_WALLET_ADDRESS_HERE_**********',
      expiresAt: generateExpirationDate('3h'),
    },
  });

  console.log('✅ Created USDT payment link:', usdtLink.hash);

  // Create an expired link for testing
  const expiredLink = await prisma.paymentLink.create({
    data: {
      hash: generatePaymentHash(),
      amount: 500000, // 500,000 VND
      currency: 'VND',
      bankAccount: '**********',
      bankCode: 'TCB',
      expiresAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    },
  });

  console.log('✅ Created expired payment link:', expiredLink.hash);

  // Create a paid link for testing
  const paidLink = await prisma.paymentLink.create({
    data: {
      hash: generatePaymentHash(),
      amount: 25.50, // 25.50 USDT
      currency: 'USDT',
      walletAddress: 'TRX_SAMPLE_WALLET_ADDRESS_PAID_**********',
      isPaid: true,
      paidAt: new Date(),
      expiresAt: generateExpirationDate('7d'),
    },
  });

  console.log('✅ Created paid payment link:', paidLink.hash);

  // Create some payment logs
  await prisma.paymentLog.create({
    data: {
      paymentLinkId: vndLink.id,
      action: 'created',
      details: JSON.stringify({ source: 'seed' }),
      ipAddress: '127.0.0.1',
      userAgent: 'Seed Script',
    },
  });

  await prisma.paymentLog.create({
    data: {
      paymentLinkId: usdtLink.id,
      action: 'viewed',
      details: JSON.stringify({ source: 'seed', viewCount: 1 }),
      ipAddress: '127.0.0.1',
      userAgent: 'Seed Script',
    },
  });

  console.log('✅ Created sample payment logs');

  // Display summary
  const totalLinks = await prisma.paymentLink.count();
  const totalLogs = await prisma.paymentLog.count();

  console.log('\n📊 Seed Summary:');
  console.log(`   Payment Links: ${totalLinks}`);
  console.log(`   Payment Logs: ${totalLogs}`);
  console.log('\n🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
