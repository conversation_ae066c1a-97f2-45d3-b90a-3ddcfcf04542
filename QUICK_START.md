# 🚀 Quick Start Guide - Payment Link Generator

## ⚡ Khởi chạy nhanh (5 phút)

### 1. <PERSON><PERSON><PERSON> bị
```bash
# Clone hoặc download project
cd PaymenLink

# Cài đặt dependencies
npm install
```

### 2. Setup Database
```bash
# Generate Prisma client
npm run db:generate

# Tạo database và tables
npm run db:push

# (Optional) Seed dữ liệu mẫu
npm run db:seed
```

### 3. Chạy Backend
```bash
# Terminal 1: Start backend server
npm run dev:simple

# Hoặc
npx ts-node src/test-server.ts
```

Kết quả:
```
🔍 Testing database connection...
✅ Database connection successful
🚀 Test server running on port 3000
📊 Health check: http://localhost:3000/api/health
```

### 4. Chạy Frontend
```bash
# Terminal 2: Start static HTML frontend
cd frontend/public
python -m http.server 8080

# Hoặc dùng Node.js
npx http-server -p 8080
```

### 5. T<PERSON><PERSON> cập ứng dụng
- **Trang chủ**: http://localhost:8080/test.html
- **API Health**: http://localhost:3000/api/health

## 🧪 Test nhanh

### Tạo payment link qua API
```bash
node create-test-link.js
```

### Test toàn bộ flow
```bash
node test-full-flow.js
```

### Test API endpoints
```bash
node test-api.js
```

## 📱 Sử dụng

### 1. Tạo link thanh toán
1. Mở http://localhost:8080/test.html
2. Điền thông tin:
   - Số tiền: `1000000`
   - Loại tiền: `VND`
   - Số tài khoản: `**********`
   - Ngân hàng: `VCB`
   - Thời hạn: `1d`
3. Nhấn "Tạo Link Thanh Toán"
4. Copy link được tạo

### 2. Xem trang thanh toán
1. Mở link vừa tạo
2. Xem thông tin chuyển khoản
3. Copy thông tin cần thiết

### 3. Đánh dấu đã thanh toán (API)
```bash
curl -X POST http://localhost:3000/api/payment/YOUR_HASH/mark-paid \
  -H "Content-Type: application/json" \
  -d '{"confirmed": true}'
```

## 🔧 Troubleshooting

### Lỗi "Module not found"
```bash
# Cài lại dependencies
rm -rf node_modules package-lock.json
npm install
```

### Lỗi Prisma
```bash
# Generate lại Prisma client
npm run db:generate
npm run db:push
```

### Lỗi CORS
- Đảm bảo backend chạy trên port 3000
- Frontend truy cập từ localhost:8080

### Port đã được sử dụng
```bash
# Tìm process đang dùng port
netstat -ano | findstr :3000
netstat -ano | findstr :8080

# Kill process (Windows)
taskkill /PID <PID> /F
```

## 📊 API Endpoints

### Tạo payment link
```http
POST http://localhost:3000/api/create-payment-link
Content-Type: application/json

{
  "amount": 1000000,
  "currency": "VND",
  "bankAccount": "**********",
  "bankCode": "VCB",
  "expiresIn": "1d"
}
```

### Lấy thông tin payment
```http
GET http://localhost:3000/api/pay/{hash}
```

### Đánh dấu đã thanh toán
```http
POST http://localhost:3000/api/payment/{hash}/mark-paid
Content-Type: application/json

{
  "confirmed": true
}
```

## 🎯 Demo URLs

Sau khi chạy test, bạn có thể truy cập:

- **Trang chủ**: http://localhost:8080/test.html
- **VND Payment**: http://localhost:8080/pay.html?hash=YOUR_VND_HASH
- **USDT Payment**: http://localhost:8080/pay.html?hash=YOUR_USDT_HASH

## 🚀 Production Deployment

### Backend (Railway/Render)
1. Deploy backend với DATABASE_URL
2. Set environment variables
3. Run migrations: `npm run db:push`

### Frontend (Vercel/Netlify)
1. Upload static HTML files
2. Update API URL trong code
3. Configure CORS

## 📞 Support

Nếu gặp vấn đề:
1. Check logs trong terminal
2. Verify ports không bị conflict
3. Đảm bảo database được tạo đúng
4. Test API endpoints với curl/Postman

---

**Happy Coding!** 🎉
