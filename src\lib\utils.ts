import crypto from 'crypto';

// Base62 characters for URL-safe hash generation
const BASE62_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

/**
 * Generate a cryptographically secure random hash for payment links
 * @param length - Length of the hash (default: 32)
 * @returns URL-safe random string
 */
export function generatePaymentHash(length: number = 32): string {
  const bytes = crypto.randomBytes(length);
  let result = '';

  for (let i = 0; i < length; i++) {
    const byte = bytes[i];
    if (byte !== undefined) {
      result += BASE62_CHARS[byte % BASE62_CHARS.length];
    }
  }

  return result;
}

/**
 * Validate if a hash is in correct format
 * @param hash - Hash to validate
 * @returns boolean
 */
export function isValidHash(hash: string): boolean {
  if (!hash || hash.length !== 32) return false;
  return /^[a-zA-Z0-9]+$/.test(hash);
}

/**
 * Generate expiration date based on duration
 * @param duration - Duration string ('3h', '1d', '3d', '7d')
 * @returns Date object
 */
export function generateExpirationDate(duration: string): Date {
  const now = new Date();

  switch (duration) {
    case '3h':
      return new Date(now.getTime() + 3 * 60 * 60 * 1000);
    case '1d':
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    case '3d':
      return new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    default:
      // Default to 24 hours
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
  }
}

/**
 * Check if a payment link has expired
 * @param expiresAt - Expiration date
 * @returns boolean
 */
export function isExpired(expiresAt: Date | null): boolean {
  if (!expiresAt) return false;
  return new Date() > expiresAt;
}

/**
 * Format currency amount for display
 * @param amount - Amount to format
 * @param currency - Currency type
 * @returns Formatted string
 */
export function formatCurrency(amount: number, currency: 'VND' | 'USDT'): string {
  if (currency === 'VND') {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  } else {
    return `${amount.toFixed(2)} USDT`;
  }
}

/**
 * Validate payment amount
 * @param amount - Amount to validate
 * @param currency - Currency type
 * @returns boolean
 */
export function isValidAmount(amount: number, currency: 'VND' | 'USDT'): boolean {
  if (amount <= 0) return false;

  if (currency === 'VND') {
    // VND: minimum 1,000, maximum 1 billion
    return amount >= 1000 && amount <= 1000000000;
  } else {
    // USDT: minimum 0.01, maximum 100,000
    return amount >= 0.01 && amount <= 100000;
  }
}

/**
 * Generate a simple error response
 * @param message - Error message
 * @param code - Error code
 * @returns Error object
 */
export function createError(message: string, code: number = 400) {
  return {
    error: true,
    message,
    code,
    timestamp: new Date().toISOString()
  };
}

/**
 * Generate a success response
 * @param data - Response data
 * @param message - Success message
 * @returns Success object
 */
export function createSuccess(data: any, message: string = 'Success') {
  return {
    error: false,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}
