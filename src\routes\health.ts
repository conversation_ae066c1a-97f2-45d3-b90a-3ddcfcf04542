import { Router, Request, Response } from 'express';
import { healthCheck } from '../lib/database';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// Health check endpoint
router.get('/', asyncHandler(async (req: any, res: any) => {
  const health = await healthCheck();

  res.json({
    status: 'healthy',
    database: health,
    server: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    },
    timestamp: new Date().toISOString()
  });
}));

export default router;
