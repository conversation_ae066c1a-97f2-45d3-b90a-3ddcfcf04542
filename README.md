# Payment Link Generator

Hệ thống tạo link thanh toán nhanh chóng và an toàn, hỗ trợ VND (chuyển khoản ngân hàng) và USDT (ví điện tử).

## 🚀 Tính năng

- ✅ **Tạo link thanh toán**: Hỗ trợ VND và USDT
- ✅ **QR Code**: Tự động tạo mã QR cho thông tin thanh toán
- ✅ **Thời hạn linh hoạt**: 3h, 1 ngày, 3 ngày, 7 ngày
- ✅ **Upload biên lai**: Người thanh toán có thể upload ảnh biên lai
- ✅ **Xác nhận thanh toán**: Người tạo link có thể đánh dấu đã nhận tiền
- ✅ **Responsive**: Giao diện thân thiện trên mọi thiết bị
- ✅ **An toàn**: <PERSON>h đư<PERSON> mã hóa, c<PERSON> thời hạn sử dụng

## 🛠️ Công nghệ sử dụng

### Backend
- **Node.js** + **Express.js**
- **Prisma ORM** + **SQLite** (có thể chuyển sang MySQL)
- **TypeScript**
- **Multer** (upload file)

### Frontend
- **Next.js 14** (App Router)
- **React** + **TypeScript**
- **Tailwind CSS**
- **React Hook Form** + **Zod** (validation)
- **QRCode.js**
- **Lucide React** (icons)

## 📦 Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd PaymenLink
```

### 2. Cài đặt Backend
```bash
# Cài đặt dependencies
npm install

# Tạo database và chạy migrations
npm run db:generate
npm run db:push

# Seed dữ liệu mẫu (optional)
npm run db:seed

# Chạy server
npm run dev
```

Backend sẽ chạy tại: http://localhost:3000

### 3. Cài đặt Frontend
```bash
cd frontend

# Cài đặt dependencies
npm install

# Chạy development server
npm run dev
```

Frontend sẽ chạy tại: http://localhost:3001

## 🔧 Cấu hình

### Backend (.env)
```env
DATABASE_URL="file:./dev.db"
PORT=3000
NODE_ENV=development
JWT_SECRET=your-secret-key
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
FRONTEND_URL=http://localhost:3001
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

## 📖 API Documentation

### Tạo payment link
```http
POST /api/create-payment-link
Content-Type: application/json

{
  "amount": 1000000,
  "currency": "VND",
  "bankAccount": "**********",
  "bankCode": "VCB",
  "expiresIn": "1d"
}
```

### Lấy thông tin payment link
```http
GET /api/pay/:hash
```

### Đánh dấu đã thanh toán
```http
POST /api/payment/:hash/mark-paid
Content-Type: application/json

{
  "confirmed": true
}
```

### Upload biên lai
```http
POST /api/payment/:hash/upload-proof
Content-Type: multipart/form-data

receipt: <file>
```

## 🎯 Cách sử dụng

### 1. Tạo link thanh toán
1. Truy cập http://localhost:3001
2. Điền thông tin:
   - Số tiền
   - Loại tiền (VND/USDT)
   - Thông tin ngân hàng hoặc ví
   - Thời hạn
3. Nhấn "Tạo Link Thanh Toán"
4. Sao chép link hoặc quét QR code để chia sẻ

### 2. Thanh toán
1. Người nhận mở link thanh toán
2. Xem thông tin chuyển khoản/ví
3. Thực hiện chuyển tiền
4. Upload ảnh biên lai (optional)

### 3. Xác nhận thanh toán
1. Người tạo link kiểm tra biên lai
2. Đánh dấu "Đã thanh toán" trong hệ thống

## 🧪 Testing

### Test Backend API
```bash
node test-api.js
```

### Tạo link test nhanh
```bash
node create-test-link.js
```

## 🚀 Production Deployment

### Backend
1. Chuyển từ SQLite sang MySQL:
   ```env
   DATABASE_URL="mysql://user:password@host:port/database"
   ```
2. Deploy lên Railway, Render, hoặc VPS
3. Cấu hình CORS cho domain production

### Frontend
1. Cập nhật API URL:
   ```env
   NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
   ```
2. Deploy lên Vercel hoặc Netlify

## 📁 Cấu trúc thư mục

```
PaymenLink/
├── src/                    # Backend source
│   ├── controllers/        # API controllers
│   ├── routes/            # Express routes
│   ├── middleware/        # Custom middleware
│   ├── lib/               # Utilities & database
│   └── types/             # TypeScript types
├── frontend/              # Next.js frontend
│   └── src/
│       ├── app/           # App router pages
│       ├── components/    # React components
│       └── lib/           # Frontend utilities
├── prisma/                # Database schema & migrations
├── uploads/               # File uploads (receipts)
└── database/              # SQL schema files
```

## 🔒 Bảo mật

- Hash được tạo ngẫu nhiên 32 ký tự
- Link có thời hạn sử dụng
- Validation đầu vào nghiêm ngặt
- File upload được giới hạn loại và kích thước
- CORS được cấu hình đúng

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 License

MIT License

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng tạo issue trên GitHub hoặc liên hệ qua email.

---

**Phát triển bởi**: Augment Agent  
**Ngày tạo**: 2025-06-21  
**Phiên bản**: 1.0.0
