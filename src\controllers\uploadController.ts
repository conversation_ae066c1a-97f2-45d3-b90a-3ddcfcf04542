import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import prisma from '../lib/database';
import { createError, createSuccess, isExpired } from '../lib/utils';
import { UploadReceiptResponse } from '../types/payment';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || 'uploads';

    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename: hash_timestamp.ext
    const { hash } = req.params;
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    cb(null, `${hash}_${timestamp}${ext}`);
  }
});

// File filter for images only
const fileFilter = (_req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed (JPEG, PNG, GIF, WebP)'));
  }
};

// Configure multer
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB default
    files: 1
  }
});

// Upload receipt for payment
export async function uploadReceipt(req: Request, res: Response) {
  const { hash } = req.params;
  const file = req.file;

  if (!hash || hash.length !== 32) {
    return res.status(400).json(createError('Invalid payment link hash'));
  }

  if (!file) {
    return res.status(400).json(createError('No file uploaded'));
  }

  try {
    const paymentLink = await prisma.paymentLink.findUnique({
      where: { hash }
    });

    if (!paymentLink) {
      // Delete uploaded file if payment link not found
      fs.unlinkSync(file.path);
      return res.status(404).json(createError('Payment link not found'));
    }

    if (isExpired(paymentLink.expiresAt)) {
      // Delete uploaded file if payment link expired
      fs.unlinkSync(file.path);
      return res.status(400).json(createError('Payment link has expired'));
    }

    if (paymentLink.isPaid) {
      // Delete uploaded file if already paid
      fs.unlinkSync(file.path);
      return res.status(400).json(createError('Payment already confirmed'));
    }

    // Delete old receipt if exists
    if (paymentLink.receiptUrl) {
      const oldFilePath = path.join(process.env.UPLOAD_DIR || 'uploads', path.basename(paymentLink.receiptUrl));
      if (fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }
    }

    // Update payment link with receipt info
    const receiptUrl = `/uploads/${file.filename}`;
    const updatedLink = await prisma.paymentLink.update({
      where: { hash },
      data: {
        receiptUrl,
        uploadedAt: new Date()
      }
    });

    // Log receipt upload
    await prisma.paymentLog.create({
      data: {
        paymentLinkId: paymentLink.id,
        action: 'receipt_uploaded',
        details: JSON.stringify({
          filename: file.filename,
          originalName: file.originalname,
          size: file.size,
          mimetype: file.mimetype
        }),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    const response: UploadReceiptResponse = {
      receiptUrl: updatedLink.receiptUrl!,
      uploadedAt: updatedLink.uploadedAt!
    };

    return res.json(createSuccess(response, 'Receipt uploaded successfully'));
  } catch (error) {
    // Delete uploaded file on error
    if (file && fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }

    console.error('Error uploading receipt:', error);
    return res.status(500).json(createError('Failed to upload receipt'));
  }
}

// Get receipt for payment
export async function getReceipt(req: Request, res: Response) {
  const { hash } = req.params;

  if (!hash || hash.length !== 32) {
    return res.status(400).json(createError('Invalid payment link hash'));
  }

  try {
    const paymentLink = await prisma.paymentLink.findUnique({
      where: { hash },
      select: { receiptUrl: true, uploadedAt: true }
    });

    if (!paymentLink) {
      return res.status(404).json(createError('Payment link not found'));
    }

    if (!paymentLink.receiptUrl) {
      return res.status(404).json(createError('No receipt uploaded for this payment'));
    }

    // Check if file exists
    const filePath = path.join(process.env.UPLOAD_DIR || 'uploads', path.basename(paymentLink.receiptUrl));
    if (!fs.existsSync(filePath)) {
      return res.status(404).json(createError('Receipt file not found'));
    }

    // Return file info (not the actual file - use static serving for that)
    return res.json(createSuccess({
      receiptUrl: paymentLink.receiptUrl,
      uploadedAt: paymentLink.uploadedAt
    }, 'Receipt information retrieved successfully'));
  } catch (error) {
    console.error('Error getting receipt:', error);
    return res.status(500).json(createError('Failed to retrieve receipt'));
  }
}
