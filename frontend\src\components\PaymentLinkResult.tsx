'use client';

import { useState, useEffect } from 'react';
import QRCode from 'qrcode';
import { PaymentLink, formatCurrency, formatDate, getTimeRemaining } from '@/lib/api';
import { Copy, Check, ExternalLink, Clock } from 'lucide-react';

interface PaymentLinkResultProps {
  paymentLink: PaymentLink;
  onCreateNew: () => void;
}

export default function PaymentLinkResult({ paymentLink, onCreateNew }: PaymentLinkResultProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  const paymentUrl = `${window.location.origin}/pay/${paymentLink.hash}`;

  useEffect(() => {
    // Generate QR code
    QRCode.toDataURL(paymentUrl, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    })
      .then(setQrCodeUrl)
      .catch(console.error);
  }, [paymentUrl]);

  useEffect(() => {
    // Update time remaining every minute
    const updateTime = () => {
      setTimeRemaining(getTimeRemaining(paymentLink.expiresAt));
    };

    updateTime();
    const interval = setInterval(updateTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [paymentLink.expiresAt]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(paymentUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const openPaymentLink = () => {
    window.open(paymentUrl, '_blank');
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Check className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Link Thanh Toán Đã Tạo!</h2>
        <p className="text-gray-600">Chia sẻ link này để nhận thanh toán</p>
      </div>

      {/* Payment Info */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Số tiền:</span>
            <p className="font-semibold text-lg">
              {formatCurrency(paymentLink.amount, paymentLink.currency)}
            </p>
          </div>
          <div>
            <span className="text-gray-500">Loại tiền:</span>
            <p className="font-semibold">{paymentLink.currency}</p>
          </div>
          <div>
            <span className="text-gray-500">Tạo lúc:</span>
            <p className="font-semibold">{formatDate(paymentLink.createdAt)}</p>
          </div>
          <div>
            <span className="text-gray-500">Hết hạn:</span>
            <p className="font-semibold flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              {timeRemaining}
            </p>
          </div>
        </div>

        {/* Payment Method Info */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <span className="text-gray-500 text-sm">Thông tin thanh toán:</span>
          {paymentLink.currency === 'VND' ? (
            <div className="mt-1">
              <p className="font-semibold">STK: {paymentLink.bankAccount}</p>
              <p className="text-sm text-gray-600">Ngân hàng: {paymentLink.bankCode}</p>
            </div>
          ) : (
            <div className="mt-1">
              <p className="font-semibold text-xs break-all">{paymentLink.walletAddress}</p>
              <p className="text-sm text-gray-600">Mạng: TRC20</p>
            </div>
          )}
        </div>
      </div>

      {/* QR Code */}
      {qrCodeUrl && (
        <div className="text-center mb-6">
          <p className="text-sm text-gray-600 mb-3">Quét mã QR để mở link:</p>
          <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
            <img src={qrCodeUrl} alt="QR Code" className="w-48 h-48" />
          </div>
        </div>
      )}

      {/* Payment Link */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Link thanh toán:
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={paymentUrl}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
          />
          <button
            onClick={copyToClipboard}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Sao chép link"
          >
            {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
          </button>
          <button
            onClick={openPaymentLink}
            className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            title="Mở link"
          >
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
        {copied && (
          <p className="text-sm text-green-600 mt-1">Đã sao chép vào clipboard!</p>
        )}
      </div>

      {/* Actions */}
      <div className="space-y-3">
        <button
          onClick={onCreateNew}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Tạo Link Mới
        </button>
        
        <button
          onClick={openPaymentLink}
          className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Xem Trang Thanh Toán
        </button>
      </div>

      {/* Warning */}
      <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-sm text-yellow-800">
          <strong>Lưu ý:</strong> Hãy lưu link này an toàn. Link sẽ hết hạn sau {timeRemaining}.
        </p>
      </div>
    </div>
  );
}
