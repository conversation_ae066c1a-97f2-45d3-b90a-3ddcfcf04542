<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông Tin <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="container mx-auto px-4 max-w-2xl">
            <!-- Loading -->
            <div id="loading" class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Đang tải thông tin thanh toán...</p>
            </div>

            <!-- Error -->
            <div id="error" class="hidden">
                <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-red-600 text-2xl">❌</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Lỗi</h2>
                    <p id="error-message" class="text-gray-600 mb-4"></p>
                    <button
                        onclick="window.location.href = 'test.html'"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Về Trang Chủ
                    </button>
                </div>
            </div>

            <!-- Payment Info -->
            <div id="payment-info" class="hidden">
                <!-- Header -->
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Thông Tin Thanh Toán
                    </h1>
                    <div id="status-badge"></div>
                </div>

                <!-- Payment Info Card -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="text-center mb-6">
                        <h2 id="amount-display" class="text-2xl font-bold text-gray-900 mb-2"></h2>
                        <p class="text-gray-600">Số tiền cần thanh toán</p>
                    </div>

                    <!-- Payment Method -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 id="payment-method-title" class="text-lg font-semibold text-gray-900 mb-4"></h3>
                        
                        <div id="payment-details" class="space-y-3"></div>
                    </div>
                </div>

                <!-- Payment Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Trạng Thái</h3>
                    <div id="status-details" class="space-y-3"></div>
                </div>

                <!-- Actions -->
                <div class="mt-6 text-center">
                    <button
                        onclick="window.location.href = 'test.html'"
                        class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    >
                        Tạo Link Mới
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let paymentData = null;
        let copied = false;

        // Get hash from URL
        function getHashFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('hash');
        }

        // Format currency
        function formatCurrency(amount, currency) {
            if (currency === 'VND') {
                return amount.toLocaleString() + ' VND';
            }
            return amount + ' ' + currency;
        }

        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('vi-VN');
        }

        // Check if expired
        function isExpired(expiresAt) {
            if (!expiresAt) return false;
            return new Date() > new Date(expiresAt);
        }

        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                copied = true;
                setTimeout(() => copied = false, 2000);
                alert('Đã sao chép!');
            });
        }

        // Load payment data
        async function loadPaymentData() {
            const hash = getHashFromUrl();
            
            if (!hash) {
                showError('Không tìm thấy mã thanh toán trong URL');
                return;
            }

            try {
                const response = await fetch(`http://localhost:3000/api/pay/${hash}`);
                const result = await response.json();
                
                if (result.error) {
                    showError(result.message);
                } else {
                    paymentData = result.data;
                    showPaymentInfo();
                }
            } catch (err) {
                showError('Không thể tải thông tin thanh toán');
            }
        }

        // Show error
        function showError(message) {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('error-message').textContent = message;
            document.getElementById('error').classList.remove('hidden');
        }

        // Show payment info
        function showPaymentInfo() {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('payment-info').classList.remove('hidden');

            const expired = isExpired(paymentData.expiresAt);
            
            // Status badge
            const statusBadge = document.getElementById('status-badge');
            if (paymentData.isPaid) {
                statusBadge.innerHTML = '<div class="text-green-600"><span>✅ Đã thanh toán</span></div>';
            } else if (expired) {
                statusBadge.innerHTML = '<div class="text-red-600"><span>❌ Đã hết hạn</span></div>';
            } else {
                statusBadge.innerHTML = '<div class="text-orange-600"><span>⏳ Chờ thanh toán</span></div>';
            }

            // Amount
            document.getElementById('amount-display').textContent = 
                formatCurrency(paymentData.amount, paymentData.currency);

            // Payment method title
            document.getElementById('payment-method-title').textContent = 
                `Thông tin ${paymentData.currency === 'VND' ? 'chuyển khoản' : 'ví điện tử'}`;

            // Payment details
            const detailsContainer = document.getElementById('payment-details');
            detailsContainer.innerHTML = '';

            if (paymentData.currency === 'VND') {
                detailsContainer.innerHTML = `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <span class="text-gray-600">Số tài khoản:</span>
                        <div class="flex items-center space-x-2">
                            <span class="font-semibold">${paymentData.bankAccount}</span>
                            <button
                                onclick="copyToClipboard('${paymentData.bankAccount}')"
                                class="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                            >
                                📋
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <span class="text-gray-600">Ngân hàng:</span>
                        <span class="font-semibold">${paymentData.bankCode}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <span class="text-gray-600">Số tiền:</span>
                        <div class="flex items-center space-x-2">
                            <span class="font-semibold">${formatCurrency(paymentData.amount, paymentData.currency)}</span>
                            <button
                                onclick="copyToClipboard('${paymentData.amount}')"
                                class="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                            >
                                📋
                            </button>
                        </div>
                    </div>
                `;
            } else {
                detailsContainer.innerHTML = `
                    <div class="p-3 bg-gray-50 rounded-md">
                        <span class="text-gray-600 block mb-2">Địa chỉ ví USDT (TRC20):</span>
                        <div class="flex items-center space-x-2">
                            <span class="font-mono text-sm break-all flex-1">${paymentData.walletAddress}</span>
                            <button
                                onclick="copyToClipboard('${paymentData.walletAddress}')"
                                class="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm flex-shrink-0"
                            >
                                📋
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                        <span class="text-gray-600">Số tiền:</span>
                        <div class="flex items-center space-x-2">
                            <span class="font-semibold">${paymentData.amount} USDT</span>
                            <button
                                onclick="copyToClipboard('${paymentData.amount}')"
                                class="text-blue-600 hover:text-blue-700 px-2 py-1 text-sm"
                            >
                                📋
                            </button>
                        </div>
                    </div>
                `;
            }

            // Status details
            const statusContainer = document.getElementById('status-details');
            let statusHtml = `
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Tạo lúc:</span>
                    <span class="font-semibold">${formatDate(paymentData.createdAt)}</span>
                </div>
            `;

            if (paymentData.expiresAt) {
                statusHtml += `
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Hết hạn:</span>
                        <span class="font-semibold">${formatDate(paymentData.expiresAt)}</span>
                    </div>
                `;
            }

            statusHtml += `
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Trạng thái:</span>
                    <span class="font-semibold ${
                        paymentData.isPaid ? 'text-green-600' : 
                        expired ? 'text-red-600' : 'text-orange-600'
                    }">
                        ${paymentData.isPaid ? 'Đã thanh toán' : 
                          expired ? 'Đã hết hạn' : 'Chờ thanh toán'}
                    </span>
                </div>
            `;

            if (paymentData.paidAt) {
                statusHtml += `
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Thanh toán lúc:</span>
                        <span class="font-semibold text-green-600">${formatDate(paymentData.paidAt)}</span>
                    </div>
                `;
            }

            statusContainer.innerHTML = statusHtml;
        }

        // Load data when page loads
        window.addEventListener('load', loadPaymentData);
    </script>
</body>
</html>
